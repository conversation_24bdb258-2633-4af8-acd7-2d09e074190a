// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
  "version": "2.0.0",
  "tasks": [
    {
      "type": "npm",
      "script": "dev",
      "problemMatcher": ["$tsc-watch", "$ts-checker-webpack-watch"],
      "isBackground": true,
      "presentation": {
        "reveal": "never"
      },
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "type": "npm",
      "script": "watch",
      "problemMatcher": ["$tsc-watch", "$ts-checker-webpack-watch"],
      "isBackground": true,
      "presentation": {
        "reveal": "never"
      },
      "group": "build"
    },
    {
      "type": "npm",
      "script": "compile",
      "problemMatcher": ["$tsc", "$ts-checker-webpack"],
      "group": "build"
    }
  ]
}
