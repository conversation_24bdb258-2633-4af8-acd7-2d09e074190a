
> notebook-renderer-react-sample@0.0.1 lint
> eslint


/Users/<USER>/projects/vscode-extension-samples/notebook-renderer-react-sample/out/client/index.js
  2:22      error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:110     error  Missing semicolon                                                            @stylistic/js/semi
  2:316     error  'btoa' is not defined                                                        no-undef
  2:561     error  Missing semicolon                                                            @stylistic/js/semi
  2:607     error  Missing semicolon                                                            @stylistic/js/semi
  2:628     error  Missing semicolon                                                            @stylistic/js/semi
  2:690     error  Missing semicolon                                                            @stylistic/js/semi
  2:702     error  Missing semicolon                                                            @stylistic/js/semi
  2:724     error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:824     error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:842     error  Missing semicolon                                                            @stylistic/js/semi
  2:843     error  Expected a `for-of` loop instead of a `for` loop with this simple iteration  @typescript-eslint/prefer-for-of
  2:893     error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:972     error  Missing semicolon                                                            @stylistic/js/semi
  2:976     error  Missing semicolon                                                            @stylistic/js/semi
  2:977     error  Missing semicolon                                                            @stylistic/js/semi
  2:1367    error  Missing semicolon                                                            @stylistic/js/semi
  2:1464    error  Missing semicolon                                                            @stylistic/js/semi
  2:1534    error  Missing semicolon                                                            @stylistic/js/semi
  2:1541    error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:1552    error  Missing semicolon                                                            @stylistic/js/semi
  2:1582    error  'l' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:1715    error  Missing semicolon                                                            @stylistic/js/semi
  2:1784    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:1822    error  Expected a `for-of` loop instead of a `for` loop with this simple iteration  @typescript-eslint/prefer-for-of
  2:1849    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:1882    error  Missing semicolon                                                            @stylistic/js/semi
  2:1892    error  Missing semicolon                                                            @stylistic/js/semi
  2:1893    error  Missing semicolon                                                            @stylistic/js/semi
  2:2255    error  Missing semicolon                                                            @stylistic/js/semi
  2:2317    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:2340    error  Missing semicolon                                                            @stylistic/js/semi
  2:2398    error  Missing semicolon                                                            @stylistic/js/semi
  2:2444    error  'window' is not defined                                                      no-undef
  2:2470    error  'window' is not defined                                                      no-undef
  2:2816    error  Unexpected combined character in character class                             no-misleading-character-class
  2:2923    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:3109    error  Missing semicolon                                                            @stylistic/js/semi
  2:3119    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:3317    error  Missing semicolon                                                            @stylistic/js/semi
  2:3491    error  Missing semicolon                                                            @stylistic/js/semi
  2:3614    error  Missing semicolon                                                            @stylistic/js/semi
  2:3742    error  Missing semicolon                                                            @stylistic/js/semi
  2:4061    error  Missing semicolon                                                            @stylistic/js/semi
  2:4159    error  Missing semicolon                                                            @stylistic/js/semi
  2:4238    error  Missing semicolon                                                            @stylistic/js/semi
  2:4324    error  Missing semicolon                                                            @stylistic/js/semi
  2:4414    error  Missing semicolon                                                            @stylistic/js/semi
  2:4426    error  Unnecessary escape character: \-                                             no-useless-escape
  2:4479    error  Missing semicolon                                                            @stylistic/js/semi
  2:4508    error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:4536    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:4886    error  Missing semicolon                                                            @stylistic/js/semi
  2:5034    error  Missing semicolon                                                            @stylistic/js/semi
  2:5043    error  Missing semicolon                                                            @stylistic/js/semi
  2:5155    error  Missing semicolon                                                            @stylistic/js/semi
  2:5439    error  Missing semicolon                                                            @stylistic/js/semi
  2:5440    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:6606    error  Missing semicolon                                                            @stylistic/js/semi
  2:6795    error  Missing semicolon                                                            @stylistic/js/semi
  2:6942    error  Missing semicolon                                                            @stylistic/js/semi
  2:7039    error  Missing semicolon                                                            @stylistic/js/semi
  2:7232    error  Missing semicolon                                                            @stylistic/js/semi
  2:7490    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:7891    error  Missing semicolon                                                            @stylistic/js/semi
  2:8051    error  Missing semicolon                                                            @stylistic/js/semi
  2:8097    error  Missing semicolon                                                            @stylistic/js/semi
  2:8163    error  Missing semicolon                                                            @stylistic/js/semi
  2:8178    error  Missing semicolon                                                            @stylistic/js/semi
  2:8326    error  Missing semicolon                                                            @stylistic/js/semi
  2:8399    error  Missing semicolon                                                            @stylistic/js/semi
  2:8475    error  Missing semicolon                                                            @stylistic/js/semi
  2:8492    error  Missing semicolon                                                            @stylistic/js/semi
  2:8518    error  Missing semicolon                                                            @stylistic/js/semi
  2:8536    error  Missing semicolon                                                            @stylistic/js/semi
  2:8549    error  Missing semicolon                                                            @stylistic/js/semi
  2:8569    error  Missing semicolon                                                            @stylistic/js/semi
  2:8592    error  Missing semicolon                                                            @stylistic/js/semi
  2:8605    error  Missing semicolon                                                            @stylistic/js/semi
  2:8609    error  Missing semicolon                                                            @stylistic/js/semi
  2:8889    error  Missing semicolon                                                            @stylistic/js/semi
  2:8913    error  Missing semicolon                                                            @stylistic/js/semi
  2:8924    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:8954    error  Missing semicolon                                                            @stylistic/js/semi
  2:8962    error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:8999    error  Missing semicolon                                                            @stylistic/js/semi
  2:9295    error  Missing semicolon                                                            @stylistic/js/semi
  2:9581    error  Missing semicolon                                                            @stylistic/js/semi
  2:9908    error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:9949    error  Missing semicolon                                                            @stylistic/js/semi
  2:9956    error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:9958    error  Empty block statement                                                        no-empty
  2:9972    error  Missing semicolon                                                            @stylistic/js/semi
  2:10098   error  Missing semicolon                                                            @stylistic/js/semi
  2:10205   error  Missing semicolon                                                            @stylistic/js/semi
  2:10220   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:10372   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:10559   error  Missing semicolon                                                            @stylistic/js/semi
  2:10577   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:10598   error  Missing semicolon                                                            @stylistic/js/semi
  2:10684   error  Missing semicolon                                                            @stylistic/js/semi
  2:10713   error  Missing semicolon                                                            @stylistic/js/semi
  2:10739   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:10771   error  Missing semicolon                                                            @stylistic/js/semi
  2:10773   error  Missing semicolon                                                            @stylistic/js/semi
  2:10779   error  Missing semicolon                                                            @stylistic/js/semi
  2:10951   error  Missing semicolon                                                            @stylistic/js/semi
  2:11014   error  'document' is not defined                                                    no-undef
  2:11078   error  Missing semicolon                                                            @stylistic/js/semi
  2:11085   error  't' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:11101   error  Missing semicolon                                                            @stylistic/js/semi
  2:11255   error  Missing semicolon                                                            @stylistic/js/semi
  2:11363   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:11520   error  Missing semicolon                                                            @stylistic/js/semi
  2:11538   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:11578   error  Missing semicolon                                                            @stylistic/js/semi
  2:11641   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:11808   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:11810   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:11851   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:11992   error  Missing semicolon                                                            @stylistic/js/semi
  2:12017   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:12044   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:12162   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:12240   error  Missing semicolon                                                            @stylistic/js/semi
  2:12241   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:12338   error  Missing semicolon                                                            @stylistic/js/semi
  2:12358   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:12497   error  Missing semicolon                                                            @stylistic/js/semi
  2:12612   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:12627   error  Missing semicolon                                                            @stylistic/js/semi
  2:12632   error  Missing semicolon                                                            @stylistic/js/semi
  2:12664   error  Missing semicolon                                                            @stylistic/js/semi
  2:12709   error  Expected a `for-of` loop instead of a `for` loop with this simple iteration  @typescript-eslint/prefer-for-of
  2:12774   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:12778   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:12877   error  Missing semicolon                                                            @stylistic/js/semi
  2:13001   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:13034   error  Missing semicolon                                                            @stylistic/js/semi
  2:13035   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:13060   error  Missing semicolon                                                            @stylistic/js/semi
  2:13223   error  Missing semicolon                                                            @stylistic/js/semi
  2:13403   error  Missing semicolon                                                            @stylistic/js/semi
  2:13407   error  Missing semicolon                                                            @stylistic/js/semi
  2:13408   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:13427   error  Missing semicolon                                                            @stylistic/js/semi
  2:13463   error  Missing semicolon                                                            @stylistic/js/semi
  2:13518   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:13652   error  Missing semicolon                                                            @stylistic/js/semi
  2:13688   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:13751   error  Missing semicolon                                                            @stylistic/js/semi
  2:13958   error  Missing semicolon                                                            @stylistic/js/semi
  2:14120   error  Missing semicolon                                                            @stylistic/js/semi
  2:14249   error  'document' is not defined                                                    no-undef
  2:14434   error  Missing semicolon                                                            @stylistic/js/semi
  2:14464   error  'MSApp' is not defined                                                       no-undef
  2:14507   error  'n' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:14509   error  'r' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:14512   error  'MSApp' is not defined                                                       no-undef
  2:14568   error  Missing semicolon                                                            @stylistic/js/semi
  2:14571   error  Missing semicolon                                                            @stylistic/js/semi
  2:14683   error  Missing semicolon                                                            @stylistic/js/semi
  2:14699   error  Missing semicolon                                                            @stylistic/js/semi
  2:15460   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:15505   error  Missing semicolon                                                            @stylistic/js/semi
  2:15553   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:15613   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:15668   error  Missing semicolon                                                            @stylistic/js/semi
  2:15731   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:15787   error  Missing semicolon                                                            @stylistic/js/semi
  2:15790   error  Missing semicolon                                                            @stylistic/js/semi
  2:16228   error  Missing semicolon                                                            @stylistic/js/semi
  2:16290   error  Missing semicolon                                                            @stylistic/js/semi
  2:16556   error  Missing semicolon                                                            @stylistic/js/semi
  2:16606   error  'window' is not defined                                                      no-undef
  2:16698   error  Missing semicolon                                                            @stylistic/js/semi
  2:16745   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:16817   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:16854   error  Missing semicolon                                                            @stylistic/js/semi
  2:16871   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:16899   error  Missing semicolon                                                            @stylistic/js/semi
  2:16988   error  Missing semicolon                                                            @stylistic/js/semi
  2:17018   error  Missing semicolon                                                            @stylistic/js/semi
  2:17059   error  Missing semicolon                                                            @stylistic/js/semi
  2:17111   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:17144   error  Missing semicolon                                                            @stylistic/js/semi
  2:17485   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:17599   error  Missing semicolon                                                            @stylistic/js/semi
  2:17683   error  Missing semicolon                                                            @stylistic/js/semi
  2:17713   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:17749   error  Expected to return a value in method 'get'                                   getter-return
  2:17769   error  Missing semicolon                                                            @stylistic/js/semi
  2:17773   error  'window' is not defined                                                      no-undef
  2:17811   error  'window' is not defined                                                      no-undef
  2:17851   error  Missing semicolon                                                            @stylistic/js/semi
  2:17858   error  'me' is defined but never used                                               @typescript-eslint/no-unused-vars
  2:17867   error  Missing semicolon                                                            @stylistic/js/semi
  2:17886   error  'r' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:17888   error  'l' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:17890   error  'a' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:17892   error  'o' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:17894   error  'u' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:17896   error  'i' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:17961   error  Missing semicolon                                                            @stylistic/js/semi
  2:17986   error  Missing semicolon                                                            @stylistic/js/semi
  2:18044   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:18054   error  Missing semicolon                                                            @stylistic/js/semi
  2:18069   error  'e' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18071   error  't' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18073   error  'n' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18075   error  'r' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18077   error  'l' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18079   error  'a' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18081   error  'o' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18083   error  'u' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18085   error  'i' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:18088   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:18124   error  Missing semicolon                                                            @stylistic/js/semi
  2:18205   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:18250   error  Missing semicolon                                                            @stylistic/js/semi
  2:18259   error  Missing semicolon                                                            @stylistic/js/semi
  2:18283   error  Missing semicolon                                                            @stylistic/js/semi
  2:18421   error  Missing semicolon                                                            @stylistic/js/semi
  2:18433   error  Missing semicolon                                                            @stylistic/js/semi
  2:18481   error  Missing semicolon                                                            @stylistic/js/semi
  2:18598   error  Missing semicolon                                                            @stylistic/js/semi
  2:18718   error  Missing semicolon                                                            @stylistic/js/semi
  2:18724   error  Missing semicolon                                                            @stylistic/js/semi
  2:18824   error  Missing semicolon                                                            @stylistic/js/semi
  2:18844   error  Missing semicolon                                                            @stylistic/js/semi
  2:18868   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:18918   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:18936   error  Missing semicolon                                                            @stylistic/js/semi
  2:18947   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:18965   error  Missing semicolon                                                            @stylistic/js/semi
  2:18977   error  Missing semicolon                                                            @stylistic/js/semi
  2:19013   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:19031   error  Missing semicolon                                                            @stylistic/js/semi
  2:19042   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:19060   error  Missing semicolon                                                            @stylistic/js/semi
  2:19072   error  Missing semicolon                                                            @stylistic/js/semi
  2:19098   error  Missing semicolon                                                            @stylistic/js/semi
  2:19138   error  Missing semicolon                                                            @stylistic/js/semi
  2:19206   error  Missing semicolon                                                            @stylistic/js/semi
  2:19285   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:19399   error  Missing semicolon                                                            @stylistic/js/semi
  2:19400   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:19437   error  Missing semicolon                                                            @stylistic/js/semi
  2:19450   error  Missing semicolon                                                            @stylistic/js/semi
  2:19536   error  Missing semicolon                                                            @stylistic/js/semi
  2:19545   error  Missing semicolon                                                            @stylistic/js/semi
  2:20007   error  Missing semicolon                                                            @stylistic/js/semi
  2:20304   error  Missing semicolon                                                            @stylistic/js/semi
  2:20505   error  Missing semicolon                                                            @stylistic/js/semi
  2:20727   error  Missing semicolon                                                            @stylistic/js/semi
  2:20730   error  Missing semicolon                                                            @stylistic/js/semi
  2:20733   error  Missing semicolon                                                            @stylistic/js/semi
  2:20834   error  Missing semicolon                                                            @stylistic/js/semi
  2:20851   error  Missing semicolon                                                            @stylistic/js/semi
  2:21072   error  Missing semicolon                                                            @stylistic/js/semi
  2:21081   error  Missing semicolon                                                            @stylistic/js/semi
  2:21101   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:21119   error  Missing semicolon                                                            @stylistic/js/semi
  2:21193   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:21232   error  Missing semicolon                                                            @stylistic/js/semi
  2:21370   error  Missing semicolon                                                            @stylistic/js/semi
  2:21380   error  Missing semicolon                                                            @stylistic/js/semi
  2:21381   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:21411   error  Missing semicolon                                                            @stylistic/js/semi
  2:21412   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:21528   error  Missing semicolon                                                            @stylistic/js/semi
  2:21546   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:21651   error  Missing semicolon                                                            @stylistic/js/semi
  2:21695   error  Missing semicolon                                                            @stylistic/js/semi
  2:21765   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:21800   error  Missing semicolon                                                            @stylistic/js/semi
  2:21914   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:22004   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:22040   error  Missing semicolon                                                            @stylistic/js/semi
  2:22154   error  Missing semicolon                                                            @stylistic/js/semi
  2:22457   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:22510   error  Missing semicolon                                                            @stylistic/js/semi
  2:22511   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:22518   error  'document' is not defined                                                    no-undef
  2:22573   error  'window' is not defined                                                      no-undef
  2:22710   error  'window' is not defined                                                      no-undef
  2:23537   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:23608   error  Missing semicolon                                                            @stylistic/js/semi
  2:24029   error  Missing semicolon                                                            @stylistic/js/semi
  2:24162   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:24213   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:24259   error  Missing semicolon                                                            @stylistic/js/semi
  2:24265   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:24420   error  Missing semicolon                                                            @stylistic/js/semi
  2:24481   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:24521   error  Missing semicolon                                                            @stylistic/js/semi
  2:24605   error  Missing semicolon                                                            @stylistic/js/semi
  2:24879   error  Missing semicolon                                                            @stylistic/js/semi
  2:24901   error  Missing semicolon                                                            @stylistic/js/semi
  2:24928   error  Missing semicolon                                                            @stylistic/js/semi
  2:24988   error  Missing semicolon                                                            @stylistic/js/semi
  2:25036   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:25103   error  Missing semicolon                                                            @stylistic/js/semi
  2:25178   error  Missing semicolon                                                            @stylistic/js/semi
  2:25296   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:25342   error  Missing semicolon                                                            @stylistic/js/semi
  2:25351   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:25363   error  Missing semicolon                                                            @stylistic/js/semi
  2:25414   error  Missing semicolon                                                            @stylistic/js/semi
  2:25493   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:25558   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:25981   error  Missing semicolon                                                            @stylistic/js/semi
  2:25990   error  Missing semicolon                                                            @stylistic/js/semi
  2:26017   error  Missing semicolon                                                            @stylistic/js/semi
  2:26034   error  Missing semicolon                                                            @stylistic/js/semi
  2:26185   error  Missing semicolon                                                            @stylistic/js/semi
  2:26278   error  Missing semicolon                                                            @stylistic/js/semi
  2:26284   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:26299   error  Missing semicolon                                                            @stylistic/js/semi
  2:26326   error  Missing semicolon                                                            @stylistic/js/semi
  2:26569   error  Missing semicolon                                                            @stylistic/js/semi
  2:26693   error  Missing semicolon                                                            @stylistic/js/semi
  2:26716   error  Missing semicolon                                                            @stylistic/js/semi
  2:26739   error  Missing semicolon                                                            @stylistic/js/semi
  2:26898   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:26900   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:27083   error  Missing semicolon                                                            @stylistic/js/semi
  2:27180   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:27220   error  Invalid typeof comparison value                                              valid-typeof
  2:27299   error  Missing semicolon                                                            @stylistic/js/semi
  2:27351   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:27393   error  Invalid typeof comparison value                                              valid-typeof
  2:27476   error  Missing semicolon                                                            @stylistic/js/semi
  2:27518   error  Missing semicolon                                                            @stylistic/js/semi
  2:27624   error  Missing semicolon                                                            @stylistic/js/semi
  2:27983   error  Missing semicolon                                                            @stylistic/js/semi
  2:28143   error  Missing semicolon                                                            @stylistic/js/semi
  2:28203   error  Missing semicolon                                                            @stylistic/js/semi
  2:28428   error  'window' is not defined                                                      no-undef
  2:28448   error  Missing semicolon                                                            @stylistic/js/semi
  2:29275   error  Missing semicolon                                                            @stylistic/js/semi
  2:29299   error  Missing semicolon                                                            @stylistic/js/semi
  2:29395   error  Missing semicolon                                                            @stylistic/js/semi
  2:29537   error  Missing semicolon                                                            @stylistic/js/semi
  2:29689   error  Missing semicolon                                                            @stylistic/js/semi
  2:29765   error  Missing semicolon                                                            @stylistic/js/semi
  2:29865   error  Missing semicolon                                                            @stylistic/js/semi
  2:30285   error  Missing semicolon                                                            @stylistic/js/semi
  2:30399   error  Missing semicolon                                                            @stylistic/js/semi
  2:30477   error  'window' is not defined                                                      no-undef
  2:30492   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:30512   error  'document' is not defined                                                    no-undef
  2:30526   error  'document' is not defined                                                    no-undef
  2:30573   error  'window' is not defined                                                      no-undef
  2:30828   error  Missing semicolon                                                            @stylistic/js/semi
  2:30903   error  Missing semicolon                                                            @stylistic/js/semi
  2:31171   error  Missing semicolon                                                            @stylistic/js/semi
  2:31193   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:31300   error  Missing semicolon                                                            @stylistic/js/semi
  2:31343   error  Missing semicolon                                                            @stylistic/js/semi
  2:31379   error  Missing semicolon                                                            @stylistic/js/semi
  2:31421   error  Missing semicolon                                                            @stylistic/js/semi
  2:31470   error  'document' is not defined                                                    no-undef
  2:31494   error  'document' is not defined                                                    no-undef
  2:31524   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:31593   error  Missing semicolon                                                            @stylistic/js/semi
  2:31599   error  Missing semicolon                                                            @stylistic/js/semi
  2:31620   error  'document' is not defined                                                    no-undef
  2:31645   error  'document' is not defined                                                    no-undef
  2:31667   error  Missing semicolon                                                            @stylistic/js/semi
  2:31682   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:31736   error  Missing semicolon                                                            @stylistic/js/semi
  2:31853   error  Missing semicolon                                                            @stylistic/js/semi
  2:31862   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:31872   error  Missing semicolon                                                            @stylistic/js/semi
  2:31895   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:31983   error  Missing semicolon                                                            @stylistic/js/semi
  2:32065   error  Missing semicolon                                                            @stylistic/js/semi
  2:32110   error  Missing semicolon                                                            @stylistic/js/semi
  2:32169   error  Missing semicolon                                                            @stylistic/js/semi
  2:32273   error  Missing semicolon                                                            @stylistic/js/semi
  2:32570   error  Missing semicolon                                                            @stylistic/js/semi
  2:32631   error  Missing semicolon                                                            @stylistic/js/semi
  2:32762   error  Missing semicolon                                                            @stylistic/js/semi
  2:32816   error  Missing semicolon                                                            @stylistic/js/semi
  2:32831   error  Missing semicolon                                                            @stylistic/js/semi
  2:32840   error  Missing semicolon                                                            @stylistic/js/semi
  2:32848   error  Missing semicolon                                                            @stylistic/js/semi
  2:33047   error  Missing semicolon                                                            @stylistic/js/semi
  2:33072   error  'window' is not defined                                                      no-undef
  2:33176   error  Missing semicolon                                                            @stylistic/js/semi
  2:33183   error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:33190   error  Missing semicolon                                                            @stylistic/js/semi
  2:33236   error  Missing semicolon                                                            @stylistic/js/semi
  2:33245   error  Missing semicolon                                                            @stylistic/js/semi
  2:33465   error  Missing semicolon                                                            @stylistic/js/semi
  2:33493   error  'document' is not defined                                                    no-undef
  2:33507   error  'document' is not defined                                                    no-undef
  2:33641   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:33807   error  'window' is not defined                                                      no-undef
  2:34055   error  Missing semicolon                                                            @stylistic/js/semi
  2:34056   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:35085   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:36186   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:36213   error  'e' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36215   error  't' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36217   error  'n' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36219   error  'r' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36221   error  'l' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36223   error  'a' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36225   error  'u' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36227   error  'i' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36229   error  'c' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  2:36300   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:36330   error  Missing semicolon                                                            @stylistic/js/semi
  2:36367   error  Missing semicolon                                                            @stylistic/js/semi
  2:36395   error  Expected a `for-of` loop instead of a `for` loop with this simple iteration  @typescript-eslint/prefer-for-of
  2:36608   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:36621   error  Missing semicolon                                                            @stylistic/js/semi
  2:36745   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:36758   error  Missing semicolon                                                            @stylistic/js/semi
  2:36793   error  Missing semicolon                                                            @stylistic/js/semi
  2:36838   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:36871   error  Missing semicolon                                                            @stylistic/js/semi
  2:36948   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:36988   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:37030   error  Missing semicolon                                                            @stylistic/js/semi
  2:37034   error  Missing semicolon                                                            @stylistic/js/semi
  2:37233   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:37241   error  Missing semicolon                                                            @stylistic/js/semi
  2:37286   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:37328   error  Missing semicolon                                                            @stylistic/js/semi
  2:37437   error  Missing semicolon                                                            @stylistic/js/semi
  2:37438   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:37688   error  Missing semicolon                                                            @stylistic/js/semi
  2:38037   error  Missing semicolon                                                            @stylistic/js/semi
  2:38122   error  Missing semicolon                                                            @stylistic/js/semi
  2:38137   error  Missing semicolon                                                            @stylistic/js/semi
  2:38149   error  Missing semicolon                                                            @stylistic/js/semi
  2:38150   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:38210   error  Missing semicolon                                                            @stylistic/js/semi
  2:38219   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:38229   error  Missing semicolon                                                            @stylistic/js/semi
  2:38358   error  Expected a 'break' statement before 'case'                                   no-fallthrough
  2:38409   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:38445   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:38545   error  Expected a 'break' statement before 'case'                                   no-fallthrough
  2:39188   error  Missing semicolon                                                            @stylistic/js/semi
  2:39401   error  Missing semicolon                                                            @stylistic/js/semi
  2:39402   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:39467   error  Missing semicolon                                                            @stylistic/js/semi
  2:39647   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:39696   error  'window' is not defined                                                      no-undef
  2:39721   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:40236   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:40260   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:40327   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:40342   error  Missing semicolon                                                            @stylistic/js/semi
  2:40349   error  Missing semicolon                                                            @stylistic/js/semi
  2:40362   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:40421   error  Missing semicolon                                                            @stylistic/js/semi
  2:40450   error  'window' is not defined                                                      no-undef
  2:40576   error  Missing semicolon                                                            @stylistic/js/semi
  2:40581   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:40822   error  'window' is not defined                                                      no-undef
  2:40846   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:41018   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:41074   error  Expected a 'break' statement before 'case'                                   no-fallthrough
  2:41109   error  Missing semicolon                                                            @stylistic/js/semi
  2:41298   error  Missing semicolon                                                            @stylistic/js/semi
  2:41307   error  Missing semicolon                                                            @stylistic/js/semi
  2:41313   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:41405   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:41844   error  Missing semicolon                                                            @stylistic/js/semi
  2:42152   error  Missing semicolon                                                            @stylistic/js/semi
  2:42224   error  Missing semicolon                                                            @stylistic/js/semi
  2:42359   error  Missing semicolon                                                            @stylistic/js/semi
  2:42367   error  Missing semicolon                                                            @stylistic/js/semi
  2:42370   error  Missing semicolon                                                            @stylistic/js/semi
  2:42435   error  Missing semicolon                                                            @stylistic/js/semi
  2:42513   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:42627   error  Missing semicolon                                                            @stylistic/js/semi
  2:42636   error  Missing semicolon                                                            @stylistic/js/semi
  2:42689   error  Missing semicolon                                                            @stylistic/js/semi
  2:42724   error  Missing semicolon                                                            @stylistic/js/semi
  2:42855   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:42974   error  Missing semicolon                                                            @stylistic/js/semi
  2:42975   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:43018   error  Missing semicolon                                                            @stylistic/js/semi
  2:43153   error  Missing semicolon                                                            @stylistic/js/semi
  2:43162   error  Missing semicolon                                                            @stylistic/js/semi
  2:43406   error  Missing semicolon                                                            @stylistic/js/semi
  2:43444   error  'setTimeout' is not defined                                                  no-undef
  2:43497   error  'clearTimeout' is not defined                                                no-undef
  2:43532   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:43602   error  Missing semicolon                                                            @stylistic/js/semi
  2:43686   error  Missing semicolon                                                            @stylistic/js/semi
  2:43695   error  Missing semicolon                                                            @stylistic/js/semi
  2:43832   error  Missing semicolon                                                            @stylistic/js/semi
  2:43837   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:43850   error  Missing semicolon                                                            @stylistic/js/semi
  2:43870   error  Missing semicolon                                                            @stylistic/js/semi
  2:43882   error  Missing semicolon                                                            @stylistic/js/semi
  2:44095   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:44195   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:44219   error  Missing semicolon                                                            @stylistic/js/semi
  2:44228   error  Missing semicolon                                                            @stylistic/js/semi
  2:44247   error  Missing semicolon                                                            @stylistic/js/semi
  2:44259   error  Missing semicolon                                                            @stylistic/js/semi
  2:44350   error  Missing semicolon                                                            @stylistic/js/semi
  2:44427   error  Missing semicolon                                                            @stylistic/js/semi
  2:44461   error  Missing semicolon                                                            @stylistic/js/semi
  2:44527   error  Missing semicolon                                                            @stylistic/js/semi
  2:44576   error  Missing semicolon                                                            @stylistic/js/semi
  2:44592   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:44633   error  Missing semicolon                                                            @stylistic/js/semi
  2:44651   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:44684   error  Missing semicolon                                                            @stylistic/js/semi
  2:45058   error  Missing semicolon                                                            @stylistic/js/semi
  2:45106   error  Missing semicolon                                                            @stylistic/js/semi
  2:45121   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:45134   error  Missing semicolon                                                            @stylistic/js/semi
  2:45193   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:45210   error  Missing semicolon                                                            @stylistic/js/semi
  2:45421   error  Missing semicolon                                                            @stylistic/js/semi
  2:45554   error  Missing semicolon                                                            @stylistic/js/semi
  2:45618   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:45719   error  Missing semicolon                                                            @stylistic/js/semi
  2:46193   error  Missing semicolon                                                            @stylistic/js/semi
  2:46339   error  Missing semicolon                                                            @stylistic/js/semi
  2:46483   error  Missing semicolon                                                            @stylistic/js/semi
  2:46524   error  Missing semicolon                                                            @stylistic/js/semi
  2:46568   error  Missing semicolon                                                            @stylistic/js/semi
  2:46606   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:46619   error  Missing semicolon                                                            @stylistic/js/semi
  2:46624   error  Missing semicolon                                                            @stylistic/js/semi
  2:46685   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:46745   error  Missing semicolon                                                            @stylistic/js/semi
  2:46761   error  Missing semicolon                                                            @stylistic/js/semi
  2:46773   error  Missing semicolon                                                            @stylistic/js/semi
  2:46830   error  Missing semicolon                                                            @stylistic/js/semi
  2:46844   error  Missing semicolon                                                            @stylistic/js/semi
  2:46959   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:46994   error  Missing semicolon                                                            @stylistic/js/semi
  2:47003   error  Missing semicolon                                                            @stylistic/js/semi
  2:47071   error  Missing semicolon                                                            @stylistic/js/semi
  2:47104   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:47142   error  Missing semicolon                                                            @stylistic/js/semi
  2:47274   error  Missing semicolon                                                            @stylistic/js/semi
  2:47280   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:47334   error  Missing semicolon                                                            @stylistic/js/semi
  2:47353   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:47463   error  Missing semicolon                                                            @stylistic/js/semi
  2:47648   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:47709   error  Missing semicolon                                                            @stylistic/js/semi
  2:47750   error  Missing semicolon                                                            @stylistic/js/semi
  2:47893   error  Missing semicolon                                                            @stylistic/js/semi
  2:47911   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:48085   error  Missing semicolon                                                            @stylistic/js/semi
  2:48172   error  Missing semicolon                                                            @stylistic/js/semi
  2:48246   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:48300   error  Missing semicolon                                                            @stylistic/js/semi
  2:48543   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:48577   error  Missing semicolon                                                            @stylistic/js/semi
  2:48594   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:48619   error  Missing semicolon                                                            @stylistic/js/semi
  2:48754   error  Missing semicolon                                                            @stylistic/js/semi
  2:48755   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:48830   error  Missing semicolon                                                            @stylistic/js/semi
  2:48992   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:49100   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:49165   error  Missing semicolon                                                            @stylistic/js/semi
  2:49257   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:49460   error  Missing semicolon                                                            @stylistic/js/semi
  2:49505   error  Expected a 'break' statement before 'case'                                   no-fallthrough
  2:49613   error  Missing semicolon                                                            @stylistic/js/semi
  2:49615   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:49692   error  Missing semicolon                                                            @stylistic/js/semi
  2:49698   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:49875   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:49936   error  Missing semicolon                                                            @stylistic/js/semi
  2:49938   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:50040   error  Missing semicolon                                                            @stylistic/js/semi
  2:50236   error  Missing semicolon                                                            @stylistic/js/semi
  2:50290   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:50395   error  Missing semicolon                                                            @stylistic/js/semi
  2:50466   error  Missing semicolon                                                            @stylistic/js/semi
  2:50549   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:50602   error  Missing semicolon                                                            @stylistic/js/semi
  2:50689   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:50750   error  Missing semicolon                                                            @stylistic/js/semi
  2:50834   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:50883   error  Missing semicolon                                                            @stylistic/js/semi
  2:51072   error  Missing semicolon                                                            @stylistic/js/semi
  2:51191   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:51467   error  Missing semicolon                                                            @stylistic/js/semi
  2:51489   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:51726   error  Missing semicolon                                                            @stylistic/js/semi
  2:51766   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:51836   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:52523   error  Missing semicolon                                                            @stylistic/js/semi
  2:52643   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:52705   error  Missing semicolon                                                            @stylistic/js/semi
  2:52857   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:52906   error  Missing semicolon                                                            @stylistic/js/semi
  2:52925   error  Missing semicolon                                                            @stylistic/js/semi
  2:53002   error  Missing semicolon                                                            @stylistic/js/semi
  2:53011   error  Missing semicolon                                                            @stylistic/js/semi
  2:53177   error  Missing semicolon                                                            @stylistic/js/semi
  2:53234   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:53331   error  Missing semicolon                                                            @stylistic/js/semi
  2:53382   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:53412   error  Missing semicolon                                                            @stylistic/js/semi
  2:53453   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:53518   error  Missing semicolon                                                            @stylistic/js/semi
  2:53577   error  Missing semicolon                                                            @stylistic/js/semi
  2:53683   error  Missing semicolon                                                            @stylistic/js/semi
  2:53741   error  Missing semicolon                                                            @stylistic/js/semi
  2:53844   error  Missing semicolon                                                            @stylistic/js/semi
  2:54022   error  Missing semicolon                                                            @stylistic/js/semi
  2:54230   error  Missing semicolon                                                            @stylistic/js/semi
  2:54337   error  Missing semicolon                                                            @stylistic/js/semi
  2:54617   error  Missing semicolon                                                            @stylistic/js/semi
  2:54681   error  Missing semicolon                                                            @stylistic/js/semi
  2:54693   error  Missing semicolon                                                            @stylistic/js/semi
  2:54987   error  Missing semicolon                                                            @stylistic/js/semi
  2:55047   error  Missing semicolon                                                            @stylistic/js/semi
  2:55059   error  Missing semicolon                                                            @stylistic/js/semi
  2:55374   error  Missing semicolon                                                            @stylistic/js/semi
  2:55435   error  Missing semicolon                                                            @stylistic/js/semi
  2:55447   error  Missing semicolon                                                            @stylistic/js/semi
  2:55533   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:55601   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:55622   error  Missing semicolon                                                            @stylistic/js/semi
  2:55623   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:55699   error  Missing semicolon                                                            @stylistic/js/semi
  2:55765   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:55839   error  Missing semicolon                                                            @stylistic/js/semi
  2:55868   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:56038   error  Missing semicolon                                                            @stylistic/js/semi
  2:56043   error  Missing semicolon                                                            @stylistic/js/semi
  2:56241   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:56312   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:56333   error  Missing semicolon                                                            @stylistic/js/semi
  2:56334   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:56410   error  Missing semicolon                                                            @stylistic/js/semi
  2:56478   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:56555   error  Missing semicolon                                                            @stylistic/js/semi
  2:56592   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:56765   error  Missing semicolon                                                            @stylistic/js/semi
  2:56770   error  Missing semicolon                                                            @stylistic/js/semi
  2:56857   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57020   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57081   error  Missing semicolon                                                            @stylistic/js/semi
  2:57115   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57183   error  Missing semicolon                                                            @stylistic/js/semi
  2:57196   error  Missing semicolon                                                            @stylistic/js/semi
  2:57197   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57215   error  Missing semicolon                                                            @stylistic/js/semi
  2:57216   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57355   error  Missing semicolon                                                            @stylistic/js/semi
  2:57521   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57580   error  Missing semicolon                                                            @stylistic/js/semi
  2:57593   error  Missing semicolon                                                            @stylistic/js/semi
  2:57594   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57612   error  Missing semicolon                                                            @stylistic/js/semi
  2:57613   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:57644   error  Missing semicolon                                                            @stylistic/js/semi
  2:57656   error  Missing semicolon                                                            @stylistic/js/semi
  2:57996   error  Missing semicolon                                                            @stylistic/js/semi
  2:58010   error  Missing semicolon                                                            @stylistic/js/semi
  2:58011   error  Missing semicolon                                                            @stylistic/js/semi
  2:58125   error  Missing semicolon                                                            @stylistic/js/semi
  2:58210   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:58335   error  Missing semicolon                                                            @stylistic/js/semi
  2:58336   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:58351   error  Missing semicolon                                                            @stylistic/js/semi
  2:58366   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:58386   error  Missing semicolon                                                            @stylistic/js/semi
  2:58453   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:58479   error  Missing semicolon                                                            @stylistic/js/semi
  2:58495   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:58526   error  Missing semicolon                                                            @stylistic/js/semi
  2:58692   error  Missing semicolon                                                            @stylistic/js/semi
  2:58773   error  Missing semicolon                                                            @stylistic/js/semi
  2:58798   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:58833   error  Missing semicolon                                                            @stylistic/js/semi
  2:58928   error  Missing semicolon                                                            @stylistic/js/semi
  2:58929   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:58966   error  Missing semicolon                                                            @stylistic/js/semi
  2:58978   error  Missing semicolon                                                            @stylistic/js/semi
  2:59046   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:59213   error  Missing semicolon                                                            @stylistic/js/semi
  2:59473   error  Missing semicolon                                                            @stylistic/js/semi
  2:59625   error  Missing semicolon                                                            @stylistic/js/semi
  2:59626   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:59650   error  Missing semicolon                                                            @stylistic/js/semi
  2:59656   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:59690   error  Missing semicolon                                                            @stylistic/js/semi
  2:59781   error  Missing semicolon                                                            @stylistic/js/semi
  2:59931   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:60167   error  Missing semicolon                                                            @stylistic/js/semi
  2:60171   error  Missing semicolon                                                            @stylistic/js/semi
  2:60176   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:60208   error  Missing semicolon                                                            @stylistic/js/semi
  2:60224   error  Missing semicolon                                                            @stylistic/js/semi
  2:60232   error  Missing semicolon                                                            @stylistic/js/semi
  2:60286   error  Missing semicolon                                                            @stylistic/js/semi
  2:60301   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:60317   error  Missing semicolon                                                            @stylistic/js/semi
  2:60342   error  Expected a `for-of` loop instead of a `for` loop with this simple iteration  @typescript-eslint/prefer-for-of
  2:60422   error  Missing semicolon                                                            @stylistic/js/semi
  2:60558   error  Missing semicolon                                                            @stylistic/js/semi
  2:60671   error  Missing semicolon                                                            @stylistic/js/semi
  2:60867   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:60924   error  Missing semicolon                                                            @stylistic/js/semi
  2:60934   error  Missing semicolon                                                            @stylistic/js/semi
  2:61035   error  Missing semicolon                                                            @stylistic/js/semi
  2:61182   error  Missing semicolon                                                            @stylistic/js/semi
  2:61261   error  Missing semicolon                                                            @stylistic/js/semi
  2:61330   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:61377   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:61531   error  Missing semicolon                                                            @stylistic/js/semi
  2:61541   error  Missing semicolon                                                            @stylistic/js/semi
  2:61592   error  Missing semicolon                                                            @stylistic/js/semi
  2:61759   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:61781   error  Missing semicolon                                                            @stylistic/js/semi
  2:61782   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:61812   error  Missing semicolon                                                            @stylistic/js/semi
  2:61826   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:61898   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:62152   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:62201   error  Missing semicolon                                                            @stylistic/js/semi
  2:62210   error  Missing semicolon                                                            @stylistic/js/semi
  2:62234   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:62354   error  Missing semicolon                                                            @stylistic/js/semi
  2:62389   error  Missing semicolon                                                            @stylistic/js/semi
  2:62575   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:62599   error  Missing semicolon                                                            @stylistic/js/semi
  2:62613   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:62719   error  Missing semicolon                                                            @stylistic/js/semi
  2:62731   error  Missing semicolon                                                            @stylistic/js/semi
  2:62982   error  Missing semicolon                                                            @stylistic/js/semi
  2:63134   error  Missing semicolon                                                            @stylistic/js/semi
  2:63315   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:63379   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:63554   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:63567   error  Missing semicolon                                                            @stylistic/js/semi
  2:63740   error  Missing semicolon                                                            @stylistic/js/semi
  2:63771   error  Missing semicolon                                                            @stylistic/js/semi
  2:63774   error  Missing semicolon                                                            @stylistic/js/semi
  2:63778   error  Missing semicolon                                                            @stylistic/js/semi
  2:63993   error  Missing semicolon                                                            @stylistic/js/semi
  2:64034   error  Missing semicolon                                                            @stylistic/js/semi
  2:64260   error  Missing semicolon                                                            @stylistic/js/semi
  2:64513   error  Missing semicolon                                                            @stylistic/js/semi
  2:64570   error  Missing semicolon                                                            @stylistic/js/semi
  2:64610   error  Missing semicolon                                                            @stylistic/js/semi
  2:64643   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:64705   error  Missing semicolon                                                            @stylistic/js/semi
  2:64869   error  Missing semicolon                                                            @stylistic/js/semi
  2:64870   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:64911   error  Missing semicolon                                                            @stylistic/js/semi
  2:64949   error  Missing semicolon                                                            @stylistic/js/semi
  2:64987   error  Missing semicolon                                                            @stylistic/js/semi
  2:65023   error  Missing semicolon                                                            @stylistic/js/semi
  2:65098   error  Missing semicolon                                                            @stylistic/js/semi
  2:65153   error  Missing semicolon                                                            @stylistic/js/semi
  2:65162   error  Missing semicolon                                                            @stylistic/js/semi
  2:65245   error  Missing semicolon                                                            @stylistic/js/semi
  2:65399   error  Missing semicolon                                                            @stylistic/js/semi
  2:65544   error  Missing semicolon                                                            @stylistic/js/semi
  2:65573   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:65603   error  Missing semicolon                                                            @stylistic/js/semi
  2:65672   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:65681   error  Missing semicolon                                                            @stylistic/js/semi
  2:65705   error  Missing semicolon                                                            @stylistic/js/semi
  2:65709   error  Missing semicolon                                                            @stylistic/js/semi
  2:66097   error  Missing semicolon                                                            @stylistic/js/semi
  2:66104   error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:66106   error  Empty block statement                                                        no-empty
  2:66117   error  Missing semicolon                                                            @stylistic/js/semi
  2:66493   error  Missing semicolon                                                            @stylistic/js/semi
  2:66621   error  Missing semicolon                                                            @stylistic/js/semi
  2:66671   error  Missing semicolon                                                            @stylistic/js/semi
  2:66762   error  Missing semicolon                                                            @stylistic/js/semi
  2:66991   error  Missing semicolon                                                            @stylistic/js/semi
  2:67153   error  Missing semicolon                                                            @stylistic/js/semi
  2:67177   error  Missing semicolon                                                            @stylistic/js/semi
  2:67187   error  Missing semicolon                                                            @stylistic/js/semi
  2:67271   error  Missing semicolon                                                            @stylistic/js/semi
  2:67412   error  Missing semicolon                                                            @stylistic/js/semi
  2:67514   error  Missing semicolon                                                            @stylistic/js/semi
  2:67585   error  Missing semicolon                                                            @stylistic/js/semi
  2:67679   error  Missing semicolon                                                            @stylistic/js/semi
  2:67697   error  Missing semicolon                                                            @stylistic/js/semi
  2:67737   error  Missing semicolon                                                            @stylistic/js/semi
  2:67939   error  Missing semicolon                                                            @stylistic/js/semi
  2:68080   error  Missing semicolon                                                            @stylistic/js/semi
  2:68104   error  Missing semicolon                                                            @stylistic/js/semi
  2:68114   error  Missing semicolon                                                            @stylistic/js/semi
  2:68179   error  Missing semicolon                                                            @stylistic/js/semi
  2:68248   error  Missing semicolon                                                            @stylistic/js/semi
  2:68450   error  Missing semicolon                                                            @stylistic/js/semi
  2:68591   error  Missing semicolon                                                            @stylistic/js/semi
  2:68615   error  Missing semicolon                                                            @stylistic/js/semi
  2:68625   error  Missing semicolon                                                            @stylistic/js/semi
  2:68690   error  Missing semicolon                                                            @stylistic/js/semi
  2:68759   error  Missing semicolon                                                            @stylistic/js/semi
  2:68889   error  Missing semicolon                                                            @stylistic/js/semi
  2:69080   error  Missing semicolon                                                            @stylistic/js/semi
  2:69335   error  Missing semicolon                                                            @stylistic/js/semi
  2:69494   error  Missing semicolon                                                            @stylistic/js/semi
  2:69620   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:69642   error  Missing semicolon                                                            @stylistic/js/semi
  2:69663   error  Missing semicolon                                                            @stylistic/js/semi
  2:69953   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:70011   error  Missing semicolon                                                            @stylistic/js/semi
  2:70017   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:70060   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:70146   error  Missing semicolon                                                            @stylistic/js/semi
  2:70176   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:70233   error  Missing semicolon                                                            @stylistic/js/semi
  2:70437   error  Missing semicolon                                                            @stylistic/js/semi
  2:70485   error  Missing semicolon                                                            @stylistic/js/semi
  2:70526   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:70873   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:71035   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:71160   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:71663   error  Missing semicolon                                                            @stylistic/js/semi
  2:71669   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:71901   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:72172   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:72257   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:73130   error  Missing semicolon                                                            @stylistic/js/semi
  2:73153   error  Missing semicolon                                                            @stylistic/js/semi
  2:73249   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:73488   error  Missing semicolon                                                            @stylistic/js/semi
  2:73522   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:73642   error  Missing semicolon                                                            @stylistic/js/semi
  2:74700   error  Missing semicolon                                                            @stylistic/js/semi
  2:75100   error  Missing semicolon                                                            @stylistic/js/semi
  2:75144   error  Missing semicolon                                                            @stylistic/js/semi
  2:75352   error  Missing semicolon                                                            @stylistic/js/semi
  2:75399   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:75436   error  Missing semicolon                                                            @stylistic/js/semi
  2:75484   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:75702   error  Missing semicolon                                                            @stylistic/js/semi
  2:75809   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:75899   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:75982   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:76017   error  Missing semicolon                                                            @stylistic/js/semi
  2:76110   error  Missing semicolon                                                            @stylistic/js/semi
  2:76111   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:76148   error  Missing semicolon                                                            @stylistic/js/semi
  2:76153   error  Missing semicolon                                                            @stylistic/js/semi
  2:76231   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:76287   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:76505   error  Missing semicolon                                                            @stylistic/js/semi
  2:76506   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:76537   error  Missing semicolon                                                            @stylistic/js/semi
  2:76663   error  Missing semicolon                                                            @stylistic/js/semi
  2:76704   error  Missing semicolon                                                            @stylistic/js/semi
  2:76941   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:77011   error  Missing semicolon                                                            @stylistic/js/semi
  2:77026   error  Missing semicolon                                                            @stylistic/js/semi
  2:77038   error  Missing semicolon                                                            @stylistic/js/semi
  2:77129   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:77167   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:77259   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:77297   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:77369   error  Missing semicolon                                                            @stylistic/js/semi
  2:77833   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:77942   error  Missing semicolon                                                            @stylistic/js/semi
  2:77970   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78054   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78278   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78358   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78401   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78481   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78504   error  Missing semicolon                                                            @stylistic/js/semi
  2:78535   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78537   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:78702   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:78784   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78821   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78878   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78922   error  Missing semicolon                                                            @stylistic/js/semi
  2:78923   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:78965   error  Missing semicolon                                                            @stylistic/js/semi
  2:79099   error  Unnecessary escape character: \/                                             no-useless-escape
  2:79381   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79458   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79564   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79621   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79672   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79709   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79790   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79895   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:79946   error  Missing semicolon                                                            @stylistic/js/semi
  2:79979   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:80009   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80285   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:80363   error  Missing semicolon                                                            @stylistic/js/semi
  2:80386   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80423   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80453   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80525   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80664   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80708   error  Missing semicolon                                                            @stylistic/js/semi
  2:80709   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80730   error  Missing semicolon                                                            @stylistic/js/semi
  2:80731   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:80759   error  Missing semicolon                                                            @stylistic/js/semi
  2:80896   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:81084   error  Missing semicolon                                                            @stylistic/js/semi
  2:81932   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:82471   error  Missing semicolon                                                            @stylistic/js/semi
  2:82483   error  Missing semicolon                                                            @stylistic/js/semi
  2:82484   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:82552   error  Missing semicolon                                                            @stylistic/js/semi
  2:82794   error  Missing semicolon                                                            @stylistic/js/semi
  2:82800   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:82893   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:82987   error  Missing semicolon                                                            @stylistic/js/semi
  2:83304   error  Missing semicolon                                                            @stylistic/js/semi
  2:83330   error  Missing semicolon                                                            @stylistic/js/semi
  2:83367   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:83758   error  Missing semicolon                                                            @stylistic/js/semi
  2:83797   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:83815   error  Missing semicolon                                                            @stylistic/js/semi
  2:83832   error  Missing semicolon                                                            @stylistic/js/semi
  2:83895   error  Missing semicolon                                                            @stylistic/js/semi
  2:83928   error  Missing semicolon                                                            @stylistic/js/semi
  2:83950   error  'console' is not defined                                                     no-undef
  2:83972   error  Missing semicolon                                                            @stylistic/js/semi
  2:83982   error  'setTimeout' is not defined                                                  no-undef
  2:84012   error  Missing semicolon                                                            @stylistic/js/semi
  2:84015   error  Missing semicolon                                                            @stylistic/js/semi
  2:84017   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84149   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84184   error  Missing semicolon                                                            @stylistic/js/semi
  2:84274   error  Missing semicolon                                                            @stylistic/js/semi
  2:84275   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84312   error  Missing semicolon                                                            @stylistic/js/semi
  2:84384   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84448   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84490   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84534   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84610   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84649   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84723   error  Missing semicolon                                                            @stylistic/js/semi
  2:84756   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:84777   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:84845   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:84847   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:84885   error  Missing semicolon                                                            @stylistic/js/semi
  2:84890   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85027   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:85129   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:85206   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85209   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:85233   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:85283   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85285   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:85338   error  Missing semicolon                                                            @stylistic/js/semi
  2:85344   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85379   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85638   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:85793   error  Missing semicolon                                                            @stylistic/js/semi
  2:85794   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85831   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85862   error  Missing semicolon                                                            @stylistic/js/semi
  2:85886   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:85905   error  Missing semicolon                                                            @stylistic/js/semi
  2:85972   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:86059   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:86083   error  Missing semicolon                                                            @stylistic/js/semi
  2:86086   error  Missing semicolon                                                            @stylistic/js/semi
  2:86242   error  Missing semicolon                                                            @stylistic/js/semi
  2:86243   error  Missing semicolon                                                            @stylistic/js/semi
  2:86342   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:86492   error  Missing semicolon                                                            @stylistic/js/semi
  2:86496   error  Missing semicolon                                                            @stylistic/js/semi
  2:86617   error  Missing semicolon                                                            @stylistic/js/semi
  2:86634   error  Missing semicolon                                                            @stylistic/js/semi
  2:86654   error  Missing semicolon                                                            @stylistic/js/semi
  2:86826   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:86948   error  Missing semicolon                                                            @stylistic/js/semi
  2:87018   error  Missing semicolon                                                            @stylistic/js/semi
  2:87038   error  Missing semicolon                                                            @stylistic/js/semi
  2:87219   error  Missing semicolon                                                            @stylistic/js/semi
  2:87228   error  Missing semicolon                                                            @stylistic/js/semi
  2:87241   error  Missing semicolon                                                            @stylistic/js/semi
  2:87322   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:87370   error  Missing semicolon                                                            @stylistic/js/semi
  2:87383   error  Missing semicolon                                                            @stylistic/js/semi
  2:87767   error  Missing semicolon                                                            @stylistic/js/semi
  2:87777   error  Missing semicolon                                                            @stylistic/js/semi
  2:88088   error  Missing semicolon                                                            @stylistic/js/semi
  2:88108   error  Missing semicolon                                                            @stylistic/js/semi
  2:88178   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:88328   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:88341   error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:88413   error  Missing semicolon                                                            @stylistic/js/semi
  2:88557   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:88592   error  Missing semicolon                                                            @stylistic/js/semi
  2:88682   error  Missing semicolon                                                            @stylistic/js/semi
  2:88683   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:88720   error  Missing semicolon                                                            @stylistic/js/semi
  2:88822   error  Missing semicolon                                                            @stylistic/js/semi
  2:88829   error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:88831   error  Empty block statement                                                        no-empty
  2:89031   error  Missing semicolon                                                            @stylistic/js/semi
  2:89048   error  Missing semicolon                                                            @stylistic/js/semi
  2:89058   error  Missing semicolon                                                            @stylistic/js/semi
  2:89071   error  Missing semicolon                                                            @stylistic/js/semi
  2:89153   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:89225   error  Missing semicolon                                                            @stylistic/js/semi
  2:89242   error  Missing semicolon                                                            @stylistic/js/semi
  2:89282   error  Missing semicolon                                                            @stylistic/js/semi
  2:89299   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:89480   error  Missing semicolon                                                            @stylistic/js/semi
  2:89534   error  Missing semicolon                                                            @stylistic/js/semi
  2:89610   error  Missing semicolon                                                            @stylistic/js/semi
  2:89630   error  Missing semicolon                                                            @stylistic/js/semi
  2:89703   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:89759   error  Missing semicolon                                                            @stylistic/js/semi
  2:89760   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:89883   error  Missing semicolon                                                            @stylistic/js/semi
  2:89894   error  Missing semicolon                                                            @stylistic/js/semi
  2:90036   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:90062   error  Missing semicolon                                                            @stylistic/js/semi
  2:90101   error  Missing semicolon                                                            @stylistic/js/semi
  2:90103   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:90124   error  Missing semicolon                                                            @stylistic/js/semi
  2:90176   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:90490   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:90511   error  Missing semicolon                                                            @stylistic/js/semi
  2:90563   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:90709   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:90730   error  Missing semicolon                                                            @stylistic/js/semi
  2:90894   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:90924   error  Missing semicolon                                                            @stylistic/js/semi
  2:90935   error  Missing semicolon                                                            @stylistic/js/semi
  2:90940   error  Missing semicolon                                                            @stylistic/js/semi
  2:91027   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91151   error  Missing semicolon                                                            @stylistic/js/semi
  2:91152   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91189   error  Missing semicolon                                                            @stylistic/js/semi
  2:91190   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91298   error  Missing semicolon                                                            @stylistic/js/semi
  2:91337   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91405   error  Missing semicolon                                                            @stylistic/js/semi
  2:91439   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91474   error  Missing semicolon                                                            @stylistic/js/semi
  2:91554   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91582   error  Missing semicolon                                                            @stylistic/js/semi
  2:91583   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91620   error  Missing semicolon                                                            @stylistic/js/semi
  2:91772   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:91840   error  Missing semicolon                                                            @stylistic/js/semi
  2:91853   error  Missing semicolon                                                            @stylistic/js/semi
  2:92160   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:92251   error  Missing semicolon                                                            @stylistic/js/semi
  2:92330   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:92559   error  Missing semicolon                                                            @stylistic/js/semi
  2:92906   error  Missing semicolon                                                            @stylistic/js/semi
  2:92926   error  Missing semicolon                                                            @stylistic/js/semi
  2:93012   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:93092   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:93124   error  Missing semicolon                                                            @stylistic/js/semi
  2:93127   error  Missing semicolon                                                            @stylistic/js/semi
  2:93261   error  Missing semicolon                                                            @stylistic/js/semi
  2:93448   error  Missing semicolon                                                            @stylistic/js/semi
  2:93619   error  Missing semicolon                                                            @stylistic/js/semi
  2:93732   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:93851   error  Missing semicolon                                                            @stylistic/js/semi
  2:94005   error  Missing semicolon                                                            @stylistic/js/semi
  2:94014   error  Missing semicolon                                                            @stylistic/js/semi
  2:94110   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:94164   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:94298   error  Missing semicolon                                                            @stylistic/js/semi
  2:94397   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:94503   error  Missing semicolon                                                            @stylistic/js/semi
  2:94680   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:94729   error  Missing semicolon                                                            @stylistic/js/semi
  2:94736   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:94767   error  Missing semicolon                                                            @stylistic/js/semi
  2:94802   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:94920   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:94933   error  Missing semicolon                                                            @stylistic/js/semi
  2:94934   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:95263   error  Missing semicolon                                                            @stylistic/js/semi
  2:95331   error  Missing semicolon                                                            @stylistic/js/semi
  2:95579   error  Missing semicolon                                                            @stylistic/js/semi
  2:95596   error  Missing semicolon                                                            @stylistic/js/semi
  2:96057   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:96101   error  Missing semicolon                                                            @stylistic/js/semi
  2:96145   error  Missing semicolon                                                            @stylistic/js/semi
  2:96243   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:96273   error  Missing semicolon                                                            @stylistic/js/semi
  2:96427   error  Missing semicolon                                                            @stylistic/js/semi
  2:96467   error  Missing semicolon                                                            @stylistic/js/semi
  2:96526   error  Missing semicolon                                                            @stylistic/js/semi
  2:96648   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:96661   error  Missing semicolon                                                            @stylistic/js/semi
  2:96761   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:96787   error  Missing semicolon                                                            @stylistic/js/semi
  2:97041   error  Missing semicolon                                                            @stylistic/js/semi
  2:97089   error  Missing semicolon                                                            @stylistic/js/semi
  2:97098   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97121   error  Missing semicolon                                                            @stylistic/js/semi
  2:97149   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97177   error  Missing semicolon                                                            @stylistic/js/semi
  2:97186   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97209   error  Missing semicolon                                                            @stylistic/js/semi
  2:97228   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97249   error  Missing semicolon                                                            @stylistic/js/semi
  2:97264   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97284   error  Missing semicolon                                                            @stylistic/js/semi
  2:97302   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97466   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97520   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97656   error  Missing semicolon                                                            @stylistic/js/semi
  2:97667   error  Missing semicolon                                                            @stylistic/js/semi
  2:97668   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97729   error  Missing semicolon                                                            @stylistic/js/semi
  2:97846   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97881   error  Missing semicolon                                                            @stylistic/js/semi
  2:97887   error  Missing semicolon                                                            @stylistic/js/semi
  2:97959   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:97982   error  Missing semicolon                                                            @stylistic/js/semi
  2:98166   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:98287   error  Missing semicolon                                                            @stylistic/js/semi
  2:98327   error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:98486   error  Missing semicolon                                                            @stylistic/js/semi
  2:98541   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:98565   error  Missing semicolon                                                            @stylistic/js/semi
  2:98702   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:98717   error  Missing semicolon                                                            @stylistic/js/semi
  2:98736   error  Missing semicolon                                                            @stylistic/js/semi
  2:98737   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:98927   error  Missing semicolon                                                            @stylistic/js/semi
  2:98928   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:98959   error  Missing semicolon                                                            @stylistic/js/semi
  2:98970   error  Missing semicolon                                                            @stylistic/js/semi
  2:99212   error  Missing semicolon                                                            @stylistic/js/semi
  2:99213   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:99266   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:99502   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:99556   error  Missing semicolon                                                            @stylistic/js/semi
  2:99568   error  Missing semicolon                                                            @stylistic/js/semi
  2:99584   error  Missing semicolon                                                            @stylistic/js/semi
  2:99590   error  Missing semicolon                                                            @stylistic/js/semi
  2:99600   error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:99646   error  Missing semicolon                                                            @stylistic/js/semi
  2:99652   error  Missing semicolon                                                            @stylistic/js/semi
  2:99719   error  Missing semicolon                                                            @stylistic/js/semi
  2:99808   error  Missing semicolon                                                            @stylistic/js/semi
  2:99825   error  Missing semicolon                                                            @stylistic/js/semi
  2:99904   error  Missing semicolon                                                            @stylistic/js/semi
  2:99941   error  Missing semicolon                                                            @stylistic/js/semi
  2:99985   error  Missing semicolon                                                            @stylistic/js/semi
  2:100028  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:100094  error  Missing semicolon                                                            @stylistic/js/semi
  2:100219  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:100276  error  Missing semicolon                                                            @stylistic/js/semi
  2:100440  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:100490  error  Missing semicolon                                                            @stylistic/js/semi
  2:100491  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:100771  error  Missing semicolon                                                            @stylistic/js/semi
  2:100820  error  Missing semicolon                                                            @stylistic/js/semi
  2:100837  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:100851  error  Missing semicolon                                                            @stylistic/js/semi
  2:100914  error  Missing semicolon                                                            @stylistic/js/semi
  2:100939  error  Missing semicolon                                                            @stylistic/js/semi
  2:101177  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:101378  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:101406  error  Missing semicolon                                                            @stylistic/js/semi
  2:101751  error  'window' is not defined                                                      no-undef
  2:101814  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:101880  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:101901  error  Missing semicolon                                                            @stylistic/js/semi
  2:101908  error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:101925  error  Missing semicolon                                                            @stylistic/js/semi
  2:102124  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102253  error  Missing semicolon                                                            @stylistic/js/semi
  2:102257  error  Missing semicolon                                                            @stylistic/js/semi
  2:102295  error  Missing semicolon                                                            @stylistic/js/semi
  2:102328  error  Missing semicolon                                                            @stylistic/js/semi
  2:102341  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102413  error  Missing semicolon                                                            @stylistic/js/semi
  2:102456  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102481  error  Missing semicolon                                                            @stylistic/js/semi
  2:102500  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102635  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102690  error  Missing semicolon                                                            @stylistic/js/semi
  2:102714  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102747  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102835  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102946  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:102967  error  Missing semicolon                                                            @stylistic/js/semi
  2:102984  error  Missing semicolon                                                            @stylistic/js/semi
  2:103028  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:103053  error  Missing semicolon                                                            @stylistic/js/semi
  2:103188  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:103349  error  'document' is not defined                                                    no-undef
  2:103375  error  'window' is not defined                                                      no-undef
  2:103844  error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:103860  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:103991  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104051  error  Missing semicolon                                                            @stylistic/js/semi
  2:104052  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104222  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104270  error  Missing semicolon                                                            @stylistic/js/semi
  2:104288  error  Missing semicolon                                                            @stylistic/js/semi
  2:104332  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104357  error  Missing semicolon                                                            @stylistic/js/semi
  2:104376  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104393  error  Missing semicolon                                                            @stylistic/js/semi
  2:104417  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104458  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104739  error  Missing semicolon                                                            @stylistic/js/semi
  2:104746  error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:104748  error  Empty block statement                                                        no-empty
  2:104816  error  Missing semicolon                                                            @stylistic/js/semi
  2:104867  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:104971  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:105042  error  Missing semicolon                                                            @stylistic/js/semi
  2:105063  error  Missing semicolon                                                            @stylistic/js/semi
  2:105130  error  Missing semicolon                                                            @stylistic/js/semi
  2:105139  error  Missing semicolon                                                            @stylistic/js/semi
  2:105157  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:105215  error  Missing semicolon                                                            @stylistic/js/semi
  2:105219  error  Missing semicolon                                                            @stylistic/js/semi
  2:105237  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:105295  error  Missing semicolon                                                            @stylistic/js/semi
  2:105299  error  Missing semicolon                                                            @stylistic/js/semi
  2:105522  error  Missing semicolon                                                            @stylistic/js/semi
  2:105571  error  Missing semicolon                                                            @stylistic/js/semi
  2:105609  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:105657  error  Missing semicolon                                                            @stylistic/js/semi
  2:105706  error  Missing semicolon                                                            @stylistic/js/semi
  2:105746  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:105847  error  Missing semicolon                                                            @stylistic/js/semi
  2:105867  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:105939  error  Missing semicolon                                                            @stylistic/js/semi
  2:106044  error  Missing semicolon                                                            @stylistic/js/semi
  2:106258  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:106376  error  Missing semicolon                                                            @stylistic/js/semi
  2:106383  error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:106385  error  Empty block statement                                                        no-empty
  2:106392  error  Missing semicolon                                                            @stylistic/js/semi
  2:106404  error  Missing semicolon                                                            @stylistic/js/semi
  2:106443  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:106597  error  Missing semicolon                                                            @stylistic/js/semi
  2:106633  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:106655  error  Unexpected constant truthiness on the left-hand side of a `&&` expression    no-constant-binary-expression
  2:106806  error  Missing semicolon                                                            @stylistic/js/semi
  2:106828  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:107185  error  Missing semicolon                                                            @stylistic/js/semi
  2:107229  error  Missing semicolon                                                            @stylistic/js/semi
  2:107291  error  Missing semicolon                                                            @stylistic/js/semi
  2:107853  error  Missing semicolon                                                            @stylistic/js/semi
  2:107915  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:108018  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:108041  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:108471  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:108510  error  Missing semicolon                                                            @stylistic/js/semi
  2:108552  error  Missing semicolon                                                            @stylistic/js/semi
  2:108609  error  Missing semicolon                                                            @stylistic/js/semi
  2:108662  error  Missing semicolon                                                            @stylistic/js/semi
  2:108732  error  Missing semicolon                                                            @stylistic/js/semi
  2:108786  error  Missing semicolon                                                            @stylistic/js/semi
  2:108968  error  Missing semicolon                                                            @stylistic/js/semi
  2:108988  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:109466  error  Missing semicolon                                                            @stylistic/js/semi
  2:109739  error  Missing semicolon                                                            @stylistic/js/semi
  2:109751  error  Missing semicolon                                                            @stylistic/js/semi
  2:109787  error  Missing semicolon                                                            @stylistic/js/semi
  2:109844  error  Missing semicolon                                                            @stylistic/js/semi
  2:109849  error  Missing semicolon                                                            @stylistic/js/semi
  2:110011  error  Missing semicolon                                                            @stylistic/js/semi
  2:110089  error  Missing semicolon                                                            @stylistic/js/semi
  2:110203  error  Missing semicolon                                                            @stylistic/js/semi
  2:110222  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:110254  error  Missing semicolon                                                            @stylistic/js/semi
  2:110567  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:110705  error  Missing semicolon                                                            @stylistic/js/semi
  2:110726  error  Missing semicolon                                                            @stylistic/js/semi
  2:110866  error  Missing semicolon                                                            @stylistic/js/semi
  2:111013  error  Missing semicolon                                                            @stylistic/js/semi
  2:111014  error  Missing semicolon                                                            @stylistic/js/semi
  2:111026  error  Missing semicolon                                                            @stylistic/js/semi
  2:111208  error  Expected a conditional expression and instead saw an assignment              no-cond-assign
  2:111280  error  Missing semicolon                                                            @stylistic/js/semi
  2:111369  error  Missing semicolon                                                            @stylistic/js/semi
  2:111370  error  Missing semicolon                                                            @stylistic/js/semi
  2:111397  error  Missing semicolon                                                            @stylistic/js/semi
  2:111400  error  Missing semicolon                                                            @stylistic/js/semi
  2:111413  error  Missing semicolon                                                            @stylistic/js/semi
  2:111700  error  Missing semicolon                                                            @stylistic/js/semi
  2:111713  error  Missing semicolon                                                            @stylistic/js/semi
  2:111714  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:111856  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:111899  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:112022  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:112302  error  Missing semicolon                                                            @stylistic/js/semi
  2:112463  error  Missing semicolon                                                            @stylistic/js/semi
  2:112480  error  Missing semicolon                                                            @stylistic/js/semi
  2:112501  error  Missing semicolon                                                            @stylistic/js/semi
  2:112841  error  Missing semicolon                                                            @stylistic/js/semi
  2:112852  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:112953  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:113067  error  Missing semicolon                                                            @stylistic/js/semi
  2:113073  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:113381  error  Missing semicolon                                                            @stylistic/js/semi
  2:113390  error  Missing semicolon                                                            @stylistic/js/semi
  2:113558  error  Missing semicolon                                                            @stylistic/js/semi
  2:113583  error  Missing semicolon                                                            @stylistic/js/semi
  2:113947  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:114134  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:114225  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:114260  error  Missing semicolon                                                            @stylistic/js/semi
  2:114266  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:114292  error  Missing semicolon                                                            @stylistic/js/semi
  2:114865  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:115181  error  Missing semicolon                                                            @stylistic/js/semi
  2:115352  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:115474  error  Missing semicolon                                                            @stylistic/js/semi
  2:115483  error  Missing semicolon                                                            @stylistic/js/semi
  2:115602  error  Missing semicolon                                                            @stylistic/js/semi
  2:115628  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:115655  error  Missing semicolon                                                            @stylistic/js/semi
  2:115666  error  Missing semicolon                                                            @stylistic/js/semi
  2:115670  error  Missing semicolon                                                            @stylistic/js/semi
  2:115671  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:115701  error  Missing semicolon                                                            @stylistic/js/semi
  2:116230  error  Missing semicolon                                                            @stylistic/js/semi
  2:116256  error  Missing semicolon                                                            @stylistic/js/semi
  2:116324  error  Missing semicolon                                                            @stylistic/js/semi
  2:116438  error  Missing semicolon                                                            @stylistic/js/semi
  2:116441  error  Missing semicolon                                                            @stylistic/js/semi
  2:116458  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:116492  error  Missing semicolon                                                            @stylistic/js/semi
  2:116509  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:116557  error  Missing semicolon                                                            @stylistic/js/semi
  2:116608  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:116625  error  Missing semicolon                                                            @stylistic/js/semi
  2:116655  error  Missing semicolon                                                            @stylistic/js/semi
  2:116956  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:116968  error  Missing semicolon                                                            @stylistic/js/semi
  2:117019  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:117061  error  Missing semicolon                                                            @stylistic/js/semi
  2:117064  error  'Oe' is a function                                                           no-func-assign
  2:117070  error  'Me' is a function                                                           no-func-assign
  2:117146  error  Missing semicolon                                                            @stylistic/js/semi
  2:117155  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:117178  error  Missing semicolon                                                            @stylistic/js/semi
  2:117181  error  'Re' is a function                                                           no-func-assign
  2:117195  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:117240  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:117271  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:117315  error  Missing semicolon                                                            @stylistic/js/semi
  2:117318  error  Missing semicolon                                                            @stylistic/js/semi
  2:117323  error  Missing semicolon                                                            @stylistic/js/semi
  2:117332  error  Missing semicolon                                                            @stylistic/js/semi
  2:117381  error  Missing semicolon                                                            @stylistic/js/semi
  2:117390  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:117413  error  Missing semicolon                                                            @stylistic/js/semi
  2:118015  error  Missing semicolon                                                            @stylistic/js/semi
  2:118091  error  Missing semicolon                                                            @stylistic/js/semi
  2:118272  error  '__REACT_DEVTOOLS_GLOBAL_HOOK__' is not defined                              no-undef
  2:118343  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:118365  error  Missing semicolon                                                            @stylistic/js/semi
  2:118372  error  'me' is defined but never used                                               @typescript-eslint/no-unused-vars
  2:118375  error  Empty block statement                                                        no-empty
  2:118378  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:118652  error  Missing semicolon                                                            @stylistic/js/semi
  2:118693  error  Missing semicolon                                                            @stylistic/js/semi
  2:118793  error  Missing semicolon                                                            @stylistic/js/semi
  2:118802  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:118811  error  Missing semicolon                                                            @stylistic/js/semi
  2:118894  error  Missing semicolon                                                            @stylistic/js/semi
  2:118975  error  Missing semicolon                                                            @stylistic/js/semi
  2:119120  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:119157  error  Missing semicolon                                                            @stylistic/js/semi
  2:119160  error  Missing semicolon                                                            @stylistic/js/semi
  2:119167  error  Missing semicolon                                                            @stylistic/js/semi
  2:119310  error  Missing semicolon                                                            @stylistic/js/semi
  2:119477  error  Missing semicolon                                                            @stylistic/js/semi
  2:119497  error  Missing semicolon                                                            @stylistic/js/semi
  2:119513  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:119601  error  '__REACT_DEVTOOLS_GLOBAL_HOOK__' is not defined                              no-undef
  2:119645  error  '__REACT_DEVTOOLS_GLOBAL_HOOK__' is not defined                              no-undef
  2:119687  error  Missing semicolon                                                            @stylistic/js/semi
  2:119697  error  'console' is not defined                                                     no-undef
  2:119713  error  Missing semicolon                                                            @stylistic/js/semi
  2:119734  error  Missing semicolon                                                            @stylistic/js/semi
  2:119779  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:119956  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:120229  error  Missing semicolon                                                            @stylistic/js/semi
  2:120594  error  Missing semicolon                                                            @stylistic/js/semi
  2:120631  error  Missing semicolon                                                            @stylistic/js/semi
  2:120751  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:120808  error  Missing semicolon                                                            @stylistic/js/semi
  2:120841  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:120898  error  Missing semicolon                                                            @stylistic/js/semi
  2:120899  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:121087  error  Missing semicolon                                                            @stylistic/js/semi
  2:121178  error  Missing semicolon                                                            @stylistic/js/semi
  2:121228  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:121502  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:121518  error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:121672  error  Missing semicolon                                                            @stylistic/js/semi
  2:121720  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:121809  error  Missing semicolon                                                            @stylistic/js/semi
  2:121874  error  Missing semicolon                                                            @stylistic/js/semi
  2:122042  error  Missing semicolon                                                            @stylistic/js/semi
  2:122045  error  Missing semicolon                                                            @stylistic/js/semi
  2:122071  error  Missing semicolon                                                            @stylistic/js/semi
  2:122109  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:122278  error  Missing semicolon                                                            @stylistic/js/semi
  2:122414  error  Missing semicolon                                                            @stylistic/js/semi
  2:122528  error  Missing semicolon                                                            @stylistic/js/semi
  2:122719  error  Missing semicolon                                                            @stylistic/js/semi
  2:122836  error  Missing semicolon                                                            @stylistic/js/semi
  2:123067  error  Missing semicolon                                                            @stylistic/js/semi
  2:123173  error  Missing semicolon                                                            @stylistic/js/semi
  2:123178  error  Missing semicolon                                                            @stylistic/js/semi
  2:123228  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:123278  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:123330  error  Missing semicolon                                                            @stylistic/js/semi
  2:123346  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:123386  error  Missing semicolon                                                            @stylistic/js/semi
  2:123389  error  Missing semicolon                                                            @stylistic/js/semi
  2:123439  error  Missing semicolon                                                            @stylistic/js/semi
  2:123530  error  Missing semicolon                                                            @stylistic/js/semi
  2:123666  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:123747  error  Missing semicolon                                                            @stylistic/js/semi
  2:123752  error  Missing semicolon                                                            @stylistic/js/semi
  2:123806  error  Missing semicolon                                                            @stylistic/js/semi
  2:123811  error  Missing semicolon                                                            @stylistic/js/semi
  2:123865  error  Missing semicolon                                                            @stylistic/js/semi
  2:123872  error  Missing semicolon                                                            @stylistic/js/semi
  2:123928  error  Missing semicolon                                                            @stylistic/js/semi
  2:124276  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:124292  error  Do not access Object.prototype method 'hasOwnProperty' from target object    no-prototype-builtins
  2:124353  error  Missing semicolon                                                            @stylistic/js/semi
  2:124477  error  Missing semicolon                                                            @stylistic/js/semi
  2:124537  error  Missing semicolon                                                            @stylistic/js/semi
  2:124759  error  Missing semicolon                                                            @stylistic/js/semi
  2:124845  error  Missing semicolon                                                            @stylistic/js/semi
  2:124890  error  Missing semicolon                                                            @stylistic/js/semi
  2:124944  error  Missing semicolon                                                            @stylistic/js/semi
  2:125042  error  Missing semicolon                                                            @stylistic/js/semi
  2:125116  error  Missing semicolon                                                            @stylistic/js/semi
  2:125173  error  Missing semicolon                                                            @stylistic/js/semi
  2:125228  error  Missing semicolon                                                            @stylistic/js/semi
  2:125310  error  Missing semicolon                                                            @stylistic/js/semi
  2:125387  error  Missing semicolon                                                            @stylistic/js/semi
  2:125452  error  Missing semicolon                                                            @stylistic/js/semi
  2:125501  error  Missing semicolon                                                            @stylistic/js/semi
  2:125560  error  Missing semicolon                                                            @stylistic/js/semi
  2:125603  error  Missing semicolon                                                            @stylistic/js/semi
  2:125650  error  Missing semicolon                                                            @stylistic/js/semi
  2:125670  error  Missing semicolon                                                            @stylistic/js/semi
  2:125702  error  Missing semicolon                                                            @stylistic/js/semi
  2:125780  error  'performance' is not defined                                                 no-undef
  2:125803  error  'performance' is not defined                                                 no-undef
  2:125855  error  Missing semicolon                                                            @stylistic/js/semi
  2:125856  error  Missing semicolon                                                            @stylistic/js/semi
  2:125925  error  Missing semicolon                                                            @stylistic/js/semi
  2:125926  error  Missing semicolon                                                            @stylistic/js/semi
  2:126063  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:126077  error  Missing semicolon                                                            @stylistic/js/semi
  2:126093  error  'setTimeout' is not defined                                                  no-undef
  2:126110  error  Missing semicolon                                                            @stylistic/js/semi
  2:126113  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:126127  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:126136  error  'setTimeout' is not defined                                                  no-undef
  2:126159  error  'setTimeout' is not defined                                                  no-undef
  2:126175  error  Missing semicolon                                                            @stylistic/js/semi
  2:126195  error  'setTimeout' is not defined                                                  no-undef
  2:126210  error  Missing semicolon                                                            @stylistic/js/semi
  2:126225  error  'clearTimeout' is not defined                                                no-undef
  2:126240  error  Missing semicolon                                                            @stylistic/js/semi
  2:126284  error  Missing semicolon                                                            @stylistic/js/semi
  2:126326  error  Missing semicolon                                                            @stylistic/js/semi
  2:126338  error  'window' is not defined                                                      no-undef
  2:126358  error  'window' is not defined                                                      no-undef
  2:126416  error  'window' is not defined                                                      no-undef
  2:126444  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:126463  error  'window' is not defined                                                      no-undef
  2:126493  error  'console' is not defined                                                     no-undef
  2:126679  error  'console' is not defined                                                     no-undef
  2:126841  error  Missing semicolon                                                            @stylistic/js/semi
  2:126871  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:126931  error  Missing semicolon                                                            @stylistic/js/semi
  2:126986  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:126997  error  'console' is not defined                                                     no-undef
  2:127151  error  Missing semicolon                                                            @stylistic/js/semi
  2:127163  error  'MessageChannel' is not defined                                              no-undef
  2:127188  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:127263  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:127304  error  Missing semicolon                                                            @stylistic/js/semi
  2:127341  error  Missing semicolon                                                            @stylistic/js/semi
  2:127352  error  Missing semicolon                                                            @stylistic/js/semi
  2:127368  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:127401  error  Missing semicolon                                                            @stylistic/js/semi
  2:127454  error  Missing semicolon                                                            @stylistic/js/semi
  2:127459  error  Missing semicolon                                                            @stylistic/js/semi
  2:127474  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:127483  error  Missing semicolon                                                            @stylistic/js/semi
  2:127484  error  Missing semicolon                                                            @stylistic/js/semi
  2:127592  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:127609  error  Missing semicolon                                                            @stylistic/js/semi
  2:127656  error  Missing semicolon                                                            @stylistic/js/semi
  2:127819  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:127920  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:127937  error  Missing semicolon                                                            @stylistic/js/semi
  2:127948  error  Missing semicolon                                                            @stylistic/js/semi
  2:127960  error  Missing semicolon                                                            @stylistic/js/semi
  2:128031  error  Missing semicolon                                                            @stylistic/js/semi
  2:128174  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128214  error  Missing semicolon                                                            @stylistic/js/semi
  2:128221  error  Missing semicolon                                                            @stylistic/js/semi
  2:128268  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128294  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128322  error  Missing semicolon                                                            @stylistic/js/semi
  2:128340  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128500  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128563  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128635  error  Missing semicolon                                                            @stylistic/js/semi
  2:128652  error  Missing semicolon                                                            @stylistic/js/semi
  2:128690  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128723  error  Missing semicolon                                                            @stylistic/js/semi
  2:128732  error  Missing semicolon                                                            @stylistic/js/semi
  2:128741  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128756  error  Missing semicolon                                                            @stylistic/js/semi
  2:128766  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:128989  error  Missing semicolon                                                            @stylistic/js/semi
  2:129031  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:129048  error  Missing semicolon                                                            @stylistic/js/semi
  2:129104  error  Missing semicolon                                                            @stylistic/js/semi
  2:129160  error  Missing semicolon                                                            @stylistic/js/semi
  2:129246  error  Missing semicolon                                                            @stylistic/js/semi
  2:129273  error  Missing semicolon                                                            @stylistic/js/semi
  2:129285  error  Missing semicolon                                                            @stylistic/js/semi
  2:129456  error  Missing semicolon                                                            @stylistic/js/semi
  2:129483  error  Missing semicolon                                                            @stylistic/js/semi
  2:129495  error  Missing semicolon                                                            @stylistic/js/semi
  2:129745  error  Missing semicolon                                                            @stylistic/js/semi
  2:129951  error  Missing semicolon                                                            @stylistic/js/semi
  2:130061  error  Missing semicolon                                                            @stylistic/js/semi
  2:130073  error  Missing semicolon                                                            @stylistic/js/semi
  2:130075  error  Missing semicolon                                                            @stylistic/js/semi
  2:130076  error  Missing semicolon                                                            @stylistic/js/semi
  2:130108  error  Missing semicolon                                                            @stylistic/js/semi
  2:130145  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:130209  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:130265  error  Missing semicolon                                                            @stylistic/js/semi
  2:130351  error  'document' is not defined                                                    no-undef
  2:130380  error  'window' is not defined                                                      no-undef
  2:130419  error  'window' is not defined                                                      no-undef
  2:130472  error  Missing semicolon                                                            @stylistic/js/semi
  2:130479  error  'e' is defined but never used                                                @typescript-eslint/no-unused-vars
  2:130488  error  Missing semicolon                                                            @stylistic/js/semi
  2:130495  error  Missing semicolon                                                            @stylistic/js/semi
  2:130507  error  Missing semicolon                                                            @stylistic/js/semi
  2:130508  error  Missing semicolon                                                            @stylistic/js/semi
  2:130596  error  Missing semicolon                                                            @stylistic/js/semi
  2:130605  error  Missing semicolon                                                            @stylistic/js/semi
  2:130795  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:130898  error  Missing semicolon                                                            @stylistic/js/semi
  2:130907  error  Missing semicolon                                                            @stylistic/js/semi
  2:130928  error  'document' is not defined                                                    no-undef
  2:131011  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:131025  error  Missing semicolon                                                            @stylistic/js/semi
  2:131087  error  Missing semicolon                                                            @stylistic/js/semi
  2:131307  error  Missing semicolon                                                            @stylistic/js/semi
  2:131316  error  Missing semicolon                                                            @stylistic/js/semi
  2:131387  error  Missing semicolon                                                            @stylistic/js/semi
  2:131539  error  'document' is not defined                                                    no-undef
  2:131581  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:131655  error  Missing semicolon                                                            @stylistic/js/semi
  2:131864  error  'btoa' is not defined                                                        no-undef
  2:132028  error  'document' is not defined                                                    no-undef
  2:132055  error  Missing semicolon                                                            @stylistic/js/semi
  2:132124  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:132181  error  Missing semicolon                                                            @stylistic/js/semi
  2:132187  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:132226  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:132298  error  Missing semicolon                                                            @stylistic/js/semi
  2:132302  error  Missing semicolon                                                            @stylistic/js/semi
  2:132410  error  Missing semicolon                                                            @stylistic/js/semi
  2:132419  error  Missing semicolon                                                            @stylistic/js/semi
  2:132420  error  Missing semicolon                                                            @stylistic/js/semi
  2:132445  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:132534  error  'window' is not defined                                                      no-undef
  2:132542  error  'document' is not defined                                                    no-undef
  2:132552  error  'document' is not defined                                                    no-undef
  2:132567  error  'window' is not defined                                                      no-undef
  2:132688  error  Expected a `for-of` loop instead of a `for` loop with this simple iteration  @typescript-eslint/prefer-for-of
  2:132747  error  Missing semicolon                                                            @stylistic/js/semi
  2:132799  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:132850  error  Missing semicolon                                                            @stylistic/js/semi
  2:132854  error  Missing semicolon                                                            @stylistic/js/semi
  2:132856  error  Missing semicolon                                                            @stylistic/js/semi
  2:132857  error  Missing semicolon                                                            @stylistic/js/semi
  2:132986  error  Missing semicolon                                                            @stylistic/js/semi
  2:132987  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:133058  error  Missing semicolon                                                            @stylistic/js/semi
  2:133087  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:133159  error  Missing semicolon                                                            @stylistic/js/semi
  2:133222  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:133379  error  Missing semicolon                                                            @stylistic/js/semi
  2:133447  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:133856  error  Missing semicolon                                                            @stylistic/js/semi
  2:133953  error  'document' is not defined                                                    no-undef
  2:133983  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:134006  error  Missing semicolon                                                            @stylistic/js/semi
  2:134131  error  'document' is not defined                                                    no-undef
  2:134161  error  Expected an assignment or function call and instead saw an expression        @typescript-eslint/no-unused-expressions
  2:134266  error  Missing semicolon                                                            @stylistic/js/semi
  2:134269  error  Missing semicolon                                                            @stylistic/js/semi
  2:134289  error  'e' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars

/Users/<USER>/projects/vscode-extension-samples/notebook-renderer-react-sample/src/client/css.d.ts
  2:17  error  A record is preferred over an index signature  @typescript-eslint/consistent-indexed-object-style

/Users/<USER>/projects/vscode-extension-samples/notebook-renderer-react-sample/webpack.config.js
   1:26  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
   1:26  error  'require' is not defined                 no-undef
   2:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
   2:14  error  'require' is not defined                 no-undef
   7:1   error  'module' is not defined                  no-undef
  12:21  error  '__dirname' is not defined               no-undef

✖ 1635 problems (1635 errors, 0 warnings)
  988 errors and 0 warnings potentially fixable with the `--fix` option.

