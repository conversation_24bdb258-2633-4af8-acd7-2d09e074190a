import { Subject } from '../types';

export const BAC_INFORMATIQUE_SUBJECTS: Subject[] = [
  {
    id: 'math',
    name: 'Mathématiques',
    coefficient: 4
  },
  {
    id: 'informatique',
    name: 'Informatique',
    coefficient: 4
  },
  {
    id: 'sciences_physiques',
    name: 'Sciences Physiques',
    coefficient: 3
  },
  {
    id: 'francais',
    name: 'Français',
    coefficient: 2
  },
  {
    id: 'arabe',
    name: '<PERSON><PERSON>',
    coefficient: 2
  },
  {
    id: 'anglais',
    name: '<PERSON><PERSON><PERSON>',
    coefficient: 2
  },
  {
    id: 'philosophie',
    name: 'Philosophie',
    coefficient: 2
  },
  {
    id: 'histoire_geo',
    name: 'Histoire-Géographie',
    coefficient: 2
  },
  {
    id: 'sport',
    name: 'Éducation Physique',
    coefficient: 1
  }
];

export const TOTAL_COEFFICIENTS = BAC_INFORMATIQUE_SUBJECTS.reduce(
  (sum, subject) => sum + subject.coefficient, 
  0
);
