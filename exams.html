<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examens des Années Précédentes - Bac Informatique Tunisie</title>
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .navigation {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-btn.secondary {
            background-color: var(--text-secondary);
            color: white;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .subject-card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .subject-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.15);
        }

        .subject-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .subject-icon {
            font-size: 2rem;
            margin-right: 12px;
        }

        .subject-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        .subject-coefficient {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .years-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
        }

        .year-btn {
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--card-background);
            color: var(--text-primary);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            text-decoration: none;
            display: block;
        }

        .year-btn:hover {
            border-color: var(--primary-color);
            background-color: var(--primary-color);
            color: white;
        }

        .year-btn.available {
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .year-btn.available:hover {
            background-color: var(--success-color);
            color: white;
        }

        .year-btn.coming-soon {
            border-color: var(--warning-color);
            color: var(--warning-color);
            cursor: not-allowed;
            opacity: 0.7;
        }

        .filters {
            display: flex;
            gap: 16px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--card-background);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 30px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 16px;
            border-radius: var(--border-radius);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .subjects-grid {
                grid-template-columns: 1fr;
            }
            
            .navigation {
                flex-direction: column;
            }
            
            .filters {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>📚 Examens des Années Précédentes</h1>
            <p>Baccalauréat Informatique Tunisie - Préparez-vous avec les sujets officiels</p>
        </header>

        <div class="navigation">
            <a href="simple.html" class="nav-btn secondary">🧮 Calculateur de Moyenne</a>
            <a href="exams.html" class="nav-btn primary">📚 Examens Passés</a>
        </div>

        <div class="stats-card">
            <h2>📊 Statistiques des Examens</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="total-exams">0</div>
                    <div class="stat-label">Examens disponibles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-subjects">10</div>
                    <div class="stat-label">Matières</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="years-covered">15</div>
                    <div class="stat-label">Années couvertes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">100%</div>
                    <div class="stat-label">Gratuit</div>
                </div>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label for="year-filter">📅 Année:</label>
                <select id="year-filter" class="filter-select">
                    <option value="all">Toutes les années</option>
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                    <option value="2020">2020</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="session-filter">📝 Session:</label>
                <select id="session-filter" class="filter-select">
                    <option value="all">Toutes les sessions</option>
                    <option value="principale">Session Principale</option>
                    <option value="controle">Session de Contrôle</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="subject-filter">📖 Matière:</label>
                <select id="subject-filter" class="filter-select">
                    <option value="all">Toutes les matières</option>
                </select>
            </div>
        </div>

        <div class="subjects-grid" id="subjects-grid">
            <!-- Subjects will be populated by JavaScript -->
        </div>
    </div>

    <script>
        // Subjects data with icons and exam availability
        const subjects = [
            { 
                id: 'math', 
                name: 'Mathématiques', 
                coefficient: 3, 
                icon: '🔢',
                description: 'Analyse, algèbre, géométrie, probabilités'
            },
            { 
                id: 'sciences_physiques', 
                name: 'Sciences Physiques', 
                coefficient: 2, 
                icon: '⚗️',
                description: 'Physique et chimie appliquées'
            },
            { 
                id: 'programmation', 
                name: 'Programmation', 
                coefficient: 3, 
                icon: '💻',
                description: 'Algorithmes, structures de données, langages'
            },
            { 
                id: 'sti', 
                name: 'STI', 
                coefficient: 3, 
                icon: '🔧',
                description: 'Sciences et Technologies de l\'Information'
            },
            { 
                id: 'francais', 
                name: 'Français', 
                coefficient: 1, 
                icon: '🇫🇷',
                description: 'Littérature, expression écrite et orale'
            },
            { 
                id: 'arabe', 
                name: 'Arabe', 
                coefficient: 1, 
                icon: '🇹🇳',
                description: 'Langue et littérature arabes'
            },
            { 
                id: 'anglais', 
                name: 'Anglais', 
                coefficient: 1, 
                icon: '🇬🇧',
                description: 'Compréhension et expression anglaises'
            },
            { 
                id: 'philosophie', 
                name: 'Philosophie', 
                coefficient: 1, 
                icon: '🤔',
                description: 'Réflexion critique et argumentation'
            },
            { 
                id: 'sport', 
                name: 'Sport', 
                coefficient: 1, 
                icon: '⚽',
                description: 'Éducation physique et sportive'
            },
            { 
                id: 'allemand', 
                name: 'Allemand', 
                coefficient: 1, 
                icon: '🇩🇪',
                description: 'Langue allemande (optionnel)'
            }
        ];

        // Available years and their status
        const examYears = [
            { year: 2024, status: 'coming-soon', sessions: [] },
            { year: 2023, status: 'available', sessions: ['principale', 'controle'] },
            { year: 2022, status: 'available', sessions: ['principale', 'controle'] },
            { year: 2021, status: 'available', sessions: ['principale', 'controle'] },
            { year: 2020, status: 'available', sessions: ['principale'] },
            { year: 2019, status: 'available', sessions: ['principale', 'controle'] },
            { year: 2018, status: 'available', sessions: ['principale', 'controle'] },
            { year: 2017, status: 'available', sessions: ['principale'] },
            { year: 2016, status: 'available', sessions: ['principale', 'controle'] },
            { year: 2015, status: 'available', sessions: ['principale'] },
            { year: 2014, status: 'coming-soon', sessions: [] },
            { year: 2013, status: 'coming-soon', sessions: [] },
            { year: 2012, status: 'coming-soon', sessions: [] },
            { year: 2011, status: 'coming-soon', sessions: [] },
            { year: 2010, status: 'coming-soon', sessions: [] }
        ];

        // Initialize the page
        function init() {
            populateSubjectFilter();
            renderSubjects();
            updateStats();
            attachEventListeners();
        }

        // Populate subject filter dropdown
        function populateSubjectFilter() {
            const subjectFilter = document.getElementById('subject-filter');
            subjects.forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.id;
                option.textContent = subject.name;
                subjectFilter.appendChild(option);
            });
        }

        // Render subjects grid
        function renderSubjects() {
            const grid = document.getElementById('subjects-grid');
            const yearFilter = document.getElementById('year-filter').value;
            const sessionFilter = document.getElementById('session-filter').value;
            const subjectFilter = document.getElementById('subject-filter').value;

            let filteredSubjects = subjects;
            if (subjectFilter !== 'all') {
                filteredSubjects = subjects.filter(s => s.id === subjectFilter);
            }

            grid.innerHTML = filteredSubjects.map(subject => `
                <div class="subject-card">
                    <div class="subject-header">
                        <div class="subject-icon">${subject.icon}</div>
                        <div>
                            <div class="subject-title">${subject.name}</div>
                            <div class="subject-coefficient">Coefficient: ${subject.coefficient}</div>
                        </div>
                    </div>
                    <p style="color: var(--text-secondary); margin-bottom: 20px; font-size: 0.9rem;">
                        ${subject.description}
                    </p>
                    <div class="years-grid">
                        ${renderYearsForSubject(subject, yearFilter, sessionFilter)}
                    </div>
                </div>
            `).join('');
        }

        // Render years for a specific subject
        function renderYearsForSubject(subject, yearFilter, sessionFilter) {
            let filteredYears = examYears;
            
            if (yearFilter !== 'all') {
                filteredYears = examYears.filter(y => y.year.toString() === yearFilter);
            }

            return filteredYears.map(yearData => {
                const hasMatchingSessions = sessionFilter === 'all' || 
                    yearData.sessions.includes(sessionFilter);
                
                if (!hasMatchingSessions && sessionFilter !== 'all') {
                    return '';
                }

                const className = yearData.status === 'available' ? 'year-btn available' : 'year-btn coming-soon';
                const onclick = yearData.status === 'available' ? 
                    `onclick="openExam('${subject.id}', ${yearData.year})"` : '';
                
                return `
                    <div class="${className}" ${onclick}>
                        ${yearData.year}
                        ${yearData.status === 'coming-soon' ? '<br><small>Bientôt</small>' : ''}
                    </div>
                `;
            }).join('');
        }

        // Open exam function
        function openExam(subjectId, year) {
            const subject = subjects.find(s => s.id === subjectId);
            alert(`📄 Ouverture de l'examen:\n\n` +
                  `Matière: ${subject.name}\n` +
                  `Année: ${year}\n` +
                  `Coefficient: ${subject.coefficient}\n\n` +
                  `Cette fonctionnalité sera bientôt disponible avec les vrais PDF d'examens !`);
        }

        // Update statistics
        function updateStats() {
            const availableExams = examYears.filter(y => y.status === 'available').length;
            const totalExams = availableExams * subjects.length;
            
            document.getElementById('total-exams').textContent = totalExams;
        }

        // Attach event listeners
        function attachEventListeners() {
            document.getElementById('year-filter').addEventListener('change', renderSubjects);
            document.getElementById('session-filter').addEventListener('change', renderSubjects);
            document.getElementById('subject-filter').addEventListener('change', renderSubjects);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
