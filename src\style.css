:root {
  --primary-color: #3b82f6;
  --secondary-color: #1e40af;
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --border-radius: 8px;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: var(--primary-color);
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 24px;
  margin-bottom: 20px;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.subject-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-background);
}

.subject-info {
  flex: 1;
}

.subject-name {
  font-weight: 600;
  color: var(--text-primary);
}

.subject-coefficient {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.grade-input {
  width: 80px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  text-align: center;
}

.grade-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.grade-input.invalid {
  border-color: var(--error-color);
}

.results-card {
  text-align: center;
}

.average-display {
  font-size: 3rem;
  font-weight: bold;
  margin: 20px 0;
}

.status-message {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.calculation-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin: 20px 0;
  padding: 20px;
  background: var(--background-color);
  border-radius: var(--border-radius);
}

.detail-item {
  text-align: center;
}

.detail-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.detail-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 4px;
}

.actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
}

.btn-secondary {
  background-color: var(--text-secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--text-primary);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  margin: 20px 0;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .subjects-grid {
    grid-template-columns: 1fr;
  }
  
  .subject-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .average-display {
    font-size: 2.5rem;
  }
  
  .calculation-details {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
}
