{"name": "notebook-renderer-react-sample", "displayName": "notebook-renderer-react-sample", "description": "Example of a Notebook renderer (visualizer) for GitHub Issues Notebook built with React.", "version": "0.0.1", "private": true, "license": "MIT", "engines": {"vscode": "^1.58.0"}, "categories": ["Other"], "contributes": {"notebookRenderer": [{"id": "notebook-renderer-react-github-issues", "entrypoint": "./out/client/index.js", "displayName": "GitHub Issues React-based Renderer", "mimeTypes": ["x-application/github-issues"]}]}, "scripts": {"vscode:prepublish": "npm run compile && node out/test/checkNoTestProvider.js", "compile": "npm run compile:extension && npm run compile:client", "compile:extension": "tsc -b", "compile:client": "webpack --mode production", "lint": "eslint", "watch": "concurrently -r \"npm:watch:*\"", "watch:extension": "tsc -b --watch", "watch:client": "webpack --mode development --watch", "dev": "concurrently -r npm:watch:extension npm:watch:client", "pretest": "npm run compile && npm run lint"}, "devDependencies": {"@eslint/js": "^9.13.0", "@stylistic/eslint-plugin": "^2.9.0", "@types/glob": "^7.1.3", "@types/node": "^20", "@types/react": "^17.0.15", "@types/react-dom": "^17.0.9", "@types/vscode": "^1.58.0", "@types/vscode-notebook-renderer": "^1.57.8", "@types/webpack-env": "^1.16.0", "concurrently": "^5.3.0", "css-loader": "^4.2.0", "eslint": "^9.13.0", "glob": "^7.1.7", "react": "^17.0.2", "react-dom": "^17.0.2", "style-loader": "^1.2.1", "ts-loader": "^9.4.2", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0", "vscode-notebook-error-overlay": "^1.0.1", "webpack": "^5.38.1", "webpack-cli": "^4.7.0"}}