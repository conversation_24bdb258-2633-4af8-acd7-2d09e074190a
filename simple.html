<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculateur Moyenne Bac Informatique Tunisie</title>
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            margin-bottom: 20px;
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .subject-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--card-background);
        }

        .subject-info {
            flex: 1;
        }

        .subject-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .subject-coefficient {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .grade-input {
            width: 80px;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            text-align: center;
        }

        .grade-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .grade-input.invalid {
            border-color: var(--error-color);
        }

        .results-card {
            text-align: center;
        }

        .average-display {
            font-size: 3rem;
            font-weight: bold;
            margin: 20px 0;
        }

        .status-message {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .calculation-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin: 20px 0;
            padding: 20px;
            background: var(--background-color);
            border-radius: var(--border-radius);
        }

        .detail-item {
            text-align: center;
        }

        .detail-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .detail-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
        }

        .btn-secondary {
            background-color: var(--text-secondary);
            color: white;
        }

        .btn-secondary:hover {
            background-color: var(--text-primary);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
            width: 0%;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .subjects-grid {
                grid-template-columns: 1fr;
            }
            
            .subject-item {
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }
            
            .average-display {
                font-size: 2.5rem;
            }
            
            .calculation-details {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎓 Calculateur Moyenne Bac Informatique</h1>
            <p>Calculez votre moyenne du baccalauréat informatique tunisien</p>
        </header>

        <div class="card">
            <h2>📚 Notes par matière</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="subjects-grid" id="subjects-grid">
                <!-- Subjects will be populated by JavaScript -->
            </div>
        </div>

        <div class="card results-card" id="results-card">
            <h2>📊 Résultats</h2>
            <div id="results-content">
                <p style="color: var(--text-secondary); font-size: 1.1rem;">
                    Entrez vos notes pour voir votre moyenne
                </p>
            </div>
        </div>

        <div class="actions">
            <button class="btn btn-secondary" id="clear-btn">🗑️ Effacer tout</button>
            <button class="btn btn-primary" id="save-btn">💾 Sauvegarder</button>
        </div>
    </div>

    <script>
        // Subjects data for bac informatique
        const subjects = [
            { id: 'math', name: 'Mathématiques', coefficient: 3, required: true },
            { id: 'sciences_physiques', name: 'Sciences Physiques', coefficient: 2, required: true },
            { id: 'programmation', name: 'Programmation', coefficient: 3, required: true },
            { id: 'sti', name: 'STI', coefficient: 3, required: true },
            { id: 'francais', name: 'Français', coefficient: 1, required: true },
            { id: 'arabe', name: 'Arabe', coefficient: 1, required: true },
            { id: 'anglais', name: 'Anglais', coefficient: 1, required: true },
            { id: 'philosophie', name: 'Philosophie', coefficient: 1, required: true },
            { id: 'sport', name: 'Sport', coefficient: 1, required: true },
            { id: 'allemand', name: 'Allemand', coefficient: 1, required: false, minGrade: 10 }
        ];

        let grades = {};

        // Load grades from localStorage
        function loadGrades() {
            try {
                const saved = localStorage.getItem('bac_informatique_grades');
                return saved ? JSON.parse(saved) : {};
            } catch (error) {
                console.warn('Could not load grades from localStorage:', error);
                return {};
            }
        }

        // Save grades to localStorage
        function saveGrades() {
            try {
                localStorage.setItem('bac_informatique_grades', JSON.stringify(grades));
            } catch (error) {
                console.warn('Could not save grades to localStorage:', error);
            }
        }

        // Validate grade input
        function validateGrade(grade) {
            const numGrade = parseFloat(grade);
            if (isNaN(numGrade) || numGrade < 0 || numGrade > 20) {
                return null;
            }
            return numGrade;
        }

        // Calculate average
        function calculateAverage() {
            let totalPoints = 0;
            let totalCoefficients = 0;
            let enteredGrades = 0;
            let requiredSubjects = subjects.filter(s => s.required).length;

            subjects.forEach(subject => {
                const grade = grades[subject.id];
                if (grade !== undefined && grade !== null && !isNaN(grade)) {
                    // For optional subjects (like Allemand), only include if grade meets minimum requirement
                    if (!subject.required && subject.minGrade && grade < subject.minGrade) {
                        // Don't include this grade in calculation
                        return;
                    }

                    totalPoints += grade * subject.coefficient;
                    totalCoefficients += subject.coefficient;
                    enteredGrades++;
                }
            });

            const average = totalCoefficients > 0 ? totalPoints / totalCoefficients : 0;
            const requiredGradesEntered = subjects.filter(s => s.required && grades[s.id] !== undefined && grades[s.id] !== null && !isNaN(grades[s.id])).length;
            const isComplete = requiredGradesEntered === requiredSubjects;

            return {
                average: Math.round(average * 100) / 100,
                totalPoints,
                totalCoefficients,
                isComplete,
                enteredGrades,
                requiredSubjects,
                requiredGradesEntered
            };
        }

        // Get grade status
        function getGradeStatus(average) {
            if (average >= 16) {
                return { status: 'excellent', color: '#22c55e', message: 'Excellent ! Très bien réussi !' };
            } else if (average >= 14) {
                return { status: 'good', color: '#3b82f6', message: 'Bien ! Bon travail !' };
            } else if (average >= 12) {
                return { status: 'satisfactory', color: '#f59e0b', message: 'Assez bien ! Continue tes efforts !' };
            } else if (average >= 10) {
                return { status: 'passing', color: '#f97316', message: 'Passable ! Tu peux faire mieux !' };
            } else {
                return { status: 'failing', color: '#ef4444', message: 'Insuffisant ! Il faut travailler davantage !' };
            }
        }

        // Render subjects
        function renderSubjects() {
            const grid = document.getElementById('subjects-grid');
            grid.innerHTML = subjects.map(subject => {
                const isOptional = !subject.required;
                const optionalInfo = isOptional ? ` (Optionnel - compte si >10)` : '';
                const optionalClass = isOptional ? ' style="opacity: 0.8; border-style: dashed;"' : '';

                return `
                    <div class="subject-item"${optionalClass}>
                        <div class="subject-info">
                            <div class="subject-name">${subject.name}${optionalInfo}</div>
                            <div class="subject-coefficient">Coefficient: ${subject.coefficient}</div>
                        </div>
                        <input
                            type="number"
                            class="grade-input"
                            id="grade-${subject.id}"
                            placeholder="0-20"
                            min="0"
                            max="20"
                            step="0.25"
                            value="${grades[subject.id] || ''}"
                        />
                    </div>
                `;
            }).join('');
        }

        // Update results display
        function updateResults() {
            const result = calculateAverage();
            const resultsContent = document.getElementById('results-content');
            
            if (result.totalCoefficients === 0) {
                resultsContent.innerHTML = `
                    <p style="color: var(--text-secondary); font-size: 1.1rem;">
                        Entrez vos notes pour voir votre moyenne
                    </p>
                `;
                return;
            }

            const status = getGradeStatus(result.average);

            resultsContent.innerHTML = `
                <div class="average-display" style="color: ${status.color}">
                    ${result.average.toFixed(2)}/20
                </div>
                
                <div class="status-message" style="color: ${status.color}">
                    ${status.message}
                </div>

                <div class="calculation-details">
                    <div class="detail-item">
                        <div class="detail-value">${result.enteredGrades}</div>
                        <div class="detail-label">Matières comptées</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">${result.requiredGradesEntered}/${result.requiredSubjects}</div>
                        <div class="detail-label">Matières obligatoires</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">${result.totalPoints.toFixed(1)}</div>
                        <div class="detail-label">Points obtenus</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">${result.totalCoefficients}</div>
                        <div class="detail-label">Coefficients</div>
                    </div>
                </div>

                ${!result.isComplete ? `
                    <p style="color: var(--warning-color); font-weight: 600;">
                        ⚠️ Moyenne partielle - ${result.requiredSubjects - result.requiredGradesEntered} matière(s) obligatoire(s) manquante(s)
                    </p>
                ` : `
                    <p style="color: var(--success-color); font-weight: 600;">
                        ✅ Toutes les matières obligatoires sont saisies !
                    </p>
                `}

                ${(() => {
                    const allemandGrade = grades['allemand'];
                    if (allemandGrade !== undefined && allemandGrade !== null && !isNaN(allemandGrade)) {
                        if (allemandGrade > 10) {
                            return `<p style="color: var(--success-color); font-weight: 600;">
                                ✅ Allemand (${allemandGrade}/20) compte dans votre moyenne !
                            </p>`;
                        } else {
                            return `<p style="color: var(--warning-color); font-weight: 600;">
                                ⚠️ Allemand (${allemandGrade}/20) ne compte pas (note ≤10)
                            </p>`;
                        }
                    }
                    return '';
                })()}
            `;
        }

        // Update progress bar
        function updateProgress() {
            const progressFill = document.getElementById('progress-fill');
            const completedSubjects = Object.keys(grades).filter(key => 
                grades[key] !== undefined && grades[key] !== null && !isNaN(grades[key])
            ).length;
            const percentage = (completedSubjects / subjects.length) * 100;
            progressFill.style.width = `${percentage}%`;
        }

        // Handle grade input
        function handleGradeInput(event, subjectId) {
            const input = event.target;
            const value = input.value.trim();
            
            if (value === '') {
                delete grades[subjectId];
                input.classList.remove('invalid');
            } else {
                const validatedGrade = validateGrade(value);
                if (validatedGrade !== null) {
                    grades[subjectId] = validatedGrade;
                    input.classList.remove('invalid');
                } else {
                    input.classList.add('invalid');
                }
            }
            
            updateResults();
            updateProgress();
        }

        // Handle grade blur
        function handleGradeBlur(event, subjectId) {
            const input = event.target;
            const value = input.value.trim();
            
            if (value !== '' && validateGrade(value) === null) {
                input.value = '';
                delete grades[subjectId];
                input.classList.remove('invalid');
                updateResults();
                updateProgress();
            }
        }

        // Clear all grades
        function clearAllGrades() {
            if (confirm('Êtes-vous sûr de vouloir effacer toutes les notes ?')) {
                grades = {};
                localStorage.removeItem('bac_informatique_grades');
                
                subjects.forEach(subject => {
                    const input = document.getElementById(`grade-${subject.id}`);
                    if (input) {
                        input.value = '';
                        input.classList.remove('invalid');
                    }
                });
                
                updateResults();
                updateProgress();
            }
        }

        // Save grades with feedback
        function saveGradesWithFeedback() {
            saveGrades();
            
            const saveBtn = document.getElementById('save-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = '✅ Sauvegardé !';
            saveBtn.style.backgroundColor = 'var(--success-color)';
            
            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.style.backgroundColor = '';
            }, 2000);
        }

        // Initialize the application
        function init() {
            grades = loadGrades();
            renderSubjects();
            updateResults();
            updateProgress();

            // Attach event listeners
            subjects.forEach(subject => {
                const input = document.getElementById(`grade-${subject.id}`);
                if (input) {
                    input.addEventListener('input', (e) => handleGradeInput(e, subject.id));
                    input.addEventListener('blur', (e) => handleGradeBlur(e, subject.id));
                }
            });

            document.getElementById('clear-btn').addEventListener('click', clearAllGrades);
            document.getElementById('save-btn').addEventListener('click', saveGradesWithFeedback);
        }

        // Start the application when the page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
