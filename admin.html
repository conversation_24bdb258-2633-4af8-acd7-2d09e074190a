<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - Examens Bac Informatique</title>
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .navigation {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-btn.secondary {
            background-color: var(--text-secondary);
            color: white;
        }

        .card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .file-upload {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 40px;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .file-upload:hover {
            border-color: var(--primary-color);
            background-color: rgba(59, 130, 246, 0.05);
        }

        .file-upload.dragover {
            border-color: var(--primary-color);
            background-color: rgba(59, 130, 246, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .upload-text {
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 8px;
            background: var(--card-background);
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .file-size {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .alert {
            padding: 16px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background-color: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .instructions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 30px;
        }

        .instructions h3 {
            margin-bottom: 16px;
        }

        .instructions ul {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔧 Administration des Examens</h1>
            <p>Gérez les fichiers PDF des examens du baccalauréat informatique</p>
        </header>

        <div class="navigation">
            <a href="simple.html" class="nav-btn secondary">🧮 Calculateur</a>
            <a href="exams.html" class="nav-btn secondary">📚 Examens</a>
            <a href="admin.html" class="nav-btn primary">🔧 Administration</a>
        </div>

        <div class="instructions">
            <h3>📋 Instructions d'Upload</h3>
            <ul>
                <li><strong>Format:</strong> Fichiers PDF uniquement</li>
                <li><strong>Nommage:</strong> {matiere}_{annee}_{session}.pdf</li>
                <li><strong>Exemple:</strong> math_2023_principale.pdf</li>
                <li><strong>Correction:</strong> Ajoutez "_correction" avant ".pdf"</li>
                <li><strong>Taille max:</strong> 50 MB par fichier</li>
            </ul>
        </div>

        <div class="card">
            <h2>📤 Upload d'Examens</h2>
            
            <div class="form-group">
                <label for="subject-select">Matière:</label>
                <select id="subject-select" class="form-control">
                    <option value="">Sélectionnez une matière</option>
                    <option value="math">Mathématiques</option>
                    <option value="sciences_physiques">Sciences Physiques</option>
                    <option value="programmation_theorique">Programmation (Théorique)</option>
                    <option value="programmation_pratique">Programmation (Pratique)</option>
                    <option value="sti_theorique">STI (Théorique)</option>
                    <option value="sti_pratique">STI (Pratique)</option>
                    <option value="francais">Français</option>
                    <option value="arabe">Arabe</option>
                    <option value="anglais">Anglais</option>
                    <option value="philosophie">Philosophie</option>
                    <option value="allemand">Allemand</option>
                </select>
            </div>

            <div class="form-group">
                <label for="year-input">Année:</label>
                <input type="number" id="year-input" class="form-control" min="2010" max="2030" placeholder="2023">
            </div>

            <div class="form-group">
                <label for="session-select">Session:</label>
                <select id="session-select" class="form-control">
                    <option value="">Sélectionnez une session</option>
                    <option value="principale">Session Principale</option>
                    <option value="controle">Session de Contrôle</option>
                </select>
            </div>

            <div class="form-group">
                <label>Type de fichier:</label>
                <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                        <input type="radio" name="file-type" value="exam" checked>
                        📄 Examen
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                        <input type="radio" name="file-type" value="correction">
                        ✅ Correction
                    </label>
                </div>
            </div>

            <div class="file-upload" id="file-upload">
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                    <strong>Cliquez pour sélectionner</strong> ou glissez-déposez vos fichiers PDF ici
                </div>
                <input type="file" id="file-input" accept=".pdf" multiple style="display: none;">
                <button type="button" class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                    Choisir des fichiers
                </button>
            </div>

            <div class="file-list" id="file-list"></div>

            <div style="margin-top: 20px; text-align: center;">
                <button type="button" class="btn btn-success" id="upload-btn" onclick="uploadFiles()" disabled>
                    📤 Uploader les fichiers
                </button>
            </div>
        </div>

        <div class="card">
            <h2>📊 Statistiques</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 16px;">
                <div style="text-align: center; padding: 16px; background: var(--background-color); border-radius: var(--border-radius);">
                    <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color);" id="total-files">0</div>
                    <div style="color: var(--text-secondary);">Fichiers uploadés</div>
                </div>
                <div style="text-align: center; padding: 16px; background: var(--background-color); border-radius: var(--border-radius);">
                    <div style="font-size: 2rem; font-weight: bold; color: var(--success-color);" id="total-size">0 MB</div>
                    <div style="color: var(--text-secondary);">Taille totale</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];

        // Initialize
        function init() {
            setupFileUpload();
            updateStats();
        }

        // Setup file upload functionality
        function setupFileUpload() {
            const fileUpload = document.getElementById('file-upload');
            const fileInput = document.getElementById('file-input');

            // Drag and drop
            fileUpload.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUpload.classList.add('dragover');
            });

            fileUpload.addEventListener('dragleave', () => {
                fileUpload.classList.remove('dragover');
            });

            fileUpload.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUpload.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });

            // File input change
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
        }

        // Handle selected files
        function handleFiles(files) {
            for (let file of files) {
                if (file.type === 'application/pdf') {
                    selectedFiles.push(file);
                } else {
                    showAlert(`❌ ${file.name} n'est pas un fichier PDF`, 'error');
                }
            }
            updateFileList();
            updateUploadButton();
        }

        // Update file list display
        function updateFileList() {
            const fileList = document.getElementById('file-list');
            fileList.innerHTML = selectedFiles.map((file, index) => `
                <div class="file-item">
                    <div class="file-info">
                        <div class="file-name">📄 ${file.name}</div>
                        <div class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                    </div>
                    <button onclick="removeFile(${index})" style="background: var(--error-color); color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">
                        🗑️ Supprimer
                    </button>
                </div>
            `).join('');
        }

        // Remove file from list
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
            updateUploadButton();
        }

        // Update upload button state
        function updateUploadButton() {
            const uploadBtn = document.getElementById('upload-btn');
            const hasFiles = selectedFiles.length > 0;
            const hasRequiredFields = document.getElementById('subject-select').value && 
                                    document.getElementById('year-input').value && 
                                    document.getElementById('session-select').value;
            
            uploadBtn.disabled = !hasFiles || !hasRequiredFields;
        }

        // Upload files to backend
        async function uploadFiles() {
            const subject = document.getElementById('subject-select').value;
            const year = document.getElementById('year-input').value;
            const session = document.getElementById('session-select').value;
            const fileType = document.querySelector('input[name="file-type"]:checked').value;

            if (!subject || !year || !session) {
                showAlert('❌ Veuillez remplir tous les champs requis', 'error');
                return;
            }

            if (selectedFiles.length === 0) {
                showAlert('❌ Veuillez sélectionner au moins un fichier', 'error');
                return;
            }

            // Show upload progress
            showAlert('📤 Upload en cours...', 'success');

            try {
                const formData = new FormData();
                formData.append('subject', subject);
                formData.append('year', year);
                formData.append('session', session);
                formData.append('fileType', fileType);

                selectedFiles.forEach(file => {
                    formData.append('files', file);
                });

                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(`✅ ${result.message}`, 'success');
                    selectedFiles = [];
                    updateFileList();
                    updateUploadButton();
                    await updateStats();
                } else {
                    showAlert(`❌ Erreur: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Upload error:', error);
                showAlert('❌ Erreur de connexion au serveur', 'error');
            }
        }

        // Show alert message
        function showAlert(message, type) {
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(alert, container.children[2]);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Update statistics from backend
        async function updateStats() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();

                if (response.ok) {
                    document.getElementById('total-files').textContent = stats.totalExams;
                    document.getElementById('total-size').textContent = `${stats.totalSize} MB`;
                } else {
                    console.error('Failed to fetch stats:', stats.error);
                    // Fallback to default values
                    document.getElementById('total-files').textContent = '0';
                    document.getElementById('total-size').textContent = '0 MB';
                }
            } catch (error) {
                console.error('Stats fetch error:', error);
                // Fallback to default values
                document.getElementById('total-files').textContent = '0';
                document.getElementById('total-size').textContent = '0 MB';
            }
        }

        // Add event listeners for form validation
        document.getElementById('subject-select').addEventListener('change', updateUploadButton);
        document.getElementById('year-input').addEventListener('input', updateUploadButton);
        document.getElementById('session-select').addEventListener('change', updateUploadButton);

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
