<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - Examens Bac Informatique</title>
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .navigation {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-btn.secondary {
            background-color: var(--text-secondary);
            color: white;
        }

        .card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 24px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            background: var(--card-background);
            color: var(--text-primary);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .file-upload {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 40px;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .file-upload:hover {
            border-color: var(--primary-color);
            background-color: rgba(59, 130, 246, 0.05);
        }

        .file-upload.dragover {
            border-color: var(--primary-color);
            background-color: rgba(59, 130, 246, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .upload-text {
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 8px;
            background: var(--card-background);
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .file-size {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .alert {
            padding: 16px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background-color: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .instructions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 30px;
        }

        .instructions h3 {
            margin-bottom: 16px;
        }

        .instructions ul {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔧 Administration des Examens</h1>
            <p>Gérez les fichiers PDF des examens du baccalauréat informatique</p>
        </header>

        <div class="navigation">
            <a href="simple.html" class="nav-btn secondary">🧮 Calculateur</a>
            <a href="exams.html" class="nav-btn secondary">📚 Examens</a>
            <a href="admin.html" class="nav-btn primary">🔧 Administration</a>
            <button id="logout-btn" class="nav-btn" style="background-color: var(--error-color); color: white;">🚪 Déconnexion</button>
        </div>

        <div id="auth-check" style="display: none;">
            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 3rem; margin-bottom: 20px;">🔒</div>
                <h2>Accès restreint</h2>
                <p style="margin: 20px 0;">Vous devez être connecté pour accéder à cette page.</p>
                <a href="login.html" style="padding: 12px 24px; background-color: var(--primary-color); color: white; text-decoration: none; border-radius: var(--border-radius); font-weight: 600;">Se connecter</a>
            </div>
        </div>

        <div id="admin-content" style="display: block;">

        <div class="instructions">
            <h3>📋 Instructions d'Upload</h3>
            <ul>
                <li><strong>Format:</strong> Fichiers PDF uniquement</li>
                <li><strong>Nommage:</strong> {matiere}_{annee}_{session}.pdf</li>
                <li><strong>Exemple:</strong> math_2023_principale.pdf</li>
                <li><strong>Correction:</strong> Ajoutez "_correction" avant ".pdf"</li>
                <li><strong>Taille max:</strong> 50 MB par fichier</li>
            </ul>
        </div>

        <div class="card">
            <h2>📤 Upload d'Examens</h2>
            
            <div class="form-group">
                <label for="subject-select">Matière:</label>
                <select id="subject-select" class="form-control">
                    <option value="">Sélectionnez une matière</option>
                    <option value="math">Mathématiques</option>
                    <option value="sciences_physiques">Sciences Physiques</option>
                    <option value="programmation_theorique">Programmation (Théorique)</option>
                    <option value="programmation_pratique">Programmation (Pratique)</option>
                    <option value="sti_theorique">STI (Théorique)</option>
                    <option value="sti_pratique">STI (Pratique)</option>
                    <option value="francais">Français</option>
                    <option value="arabe">Arabe</option>
                    <option value="anglais">Anglais</option>
                    <option value="philosophie">Philosophie</option>
                    <option value="allemand">Allemand</option>
                </select>
            </div>

            <div class="form-group">
                <label for="year-input">Année:</label>
                <input type="number" id="year-input" class="form-control" min="2010" max="2030" placeholder="2023">
            </div>

            <div class="form-group">
                <label for="session-select">Session:</label>
                <select id="session-select" class="form-control">
                    <option value="">Sélectionnez une session</option>
                    <option value="principale">Session Principale</option>
                    <option value="controle">Session de Contrôle</option>
                </select>
            </div>

            <div class="form-group">
                <label>Type de fichier:</label>
                <div style="display: flex; gap: 16px; margin-top: 8px;">
                    <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                        <input type="radio" name="file-type" value="exam" checked>
                        📄 Examen
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                        <input type="radio" name="file-type" value="correction">
                        ✅ Correction
                    </label>
                </div>
            </div>

            <div class="file-upload" id="file-upload">
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                    <strong>Cliquez pour sélectionner</strong> ou glissez-déposez vos fichiers PDF ici
                </div>
                <input type="file" id="file-input" accept=".pdf" multiple style="display: none;">
                <button type="button" class="btn btn-primary" id="choose-files-btn">
                    Choisir des fichiers
                </button>
            </div>

            <div class="file-list" id="file-list"></div>

            <!-- Alternative file input for testing -->
            <div style="margin-top: 20px; padding: 16px; border: 1px solid var(--border-color); border-radius: var(--border-radius); background: var(--background-color);">
                <label for="alt-file-input" style="display: block; margin-bottom: 8px; font-weight: 600;">
                    🔧 Alternative File Input (for testing):
                </label>
                <input type="file" id="alt-file-input" accept=".pdf" multiple style="width: 100%; padding: 8px;">
            </div>

            <div style="margin-top: 20px; text-align: center;">
                <button type="button" class="btn btn-success" id="upload-btn" disabled>
                    📤 Uploader les fichiers
                </button>
                <button type="button" class="btn" id="test-btn" style="background-color: var(--warning-color); color: white; margin-left: 10px;">
                    🧪 Test JS
                </button>
                <button type="button" class="btn" id="test-upload-btn" style="background-color: #6366f1; color: white; margin-left: 10px;">
                    🚀 Test Upload
                </button>
                <button type="button" class="btn" id="test-file-btn" style="background-color: #8b5cf6; color: white; margin-left: 10px;">
                    📁 Test File Input
                </button>
            </div>
        </div>

        <div class="card">
            <h2>📊 Statistiques</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 16px;">
                <div style="text-align: center; padding: 16px; background: var(--background-color); border-radius: var(--border-radius);">
                    <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color);" id="total-files">0</div>
                    <div style="color: var(--text-secondary);">Fichiers uploadés</div>
                </div>
                <div style="text-align: center; padding: 16px; background: var(--background-color); border-radius: var(--border-radius);">
                    <div style="font-size: 2rem; font-weight: bold; color: var(--success-color);" id="total-size">0 MB</div>
                    <div style="color: var(--text-secondary);">Taille totale</div>
                </div>
            </div>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>


